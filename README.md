# World-Class CRM System

A modern, feature-rich Customer Relationship Management (CRM) system built with HTML, CSS, and JavaScript. This system provides comprehensive tools for managing customers, deals, tasks, and calendar events.

## 🌟 Features

### 🔐 Authentication & User Management
- **User Registration**: Create new accounts with validation
- **Secure Login**: Password-protected access with session management
- **User Profiles**: Store user information and preferences
- **Demo Account**: Pre-configured demo user for testing

### 👥 Customer Management
- **Customer Database**: Store and manage customer information
- **Contact Details**: Name, email, phone, company, and address
- **Search & Filter**: Find customers quickly with search functionality
- **CRUD Operations**: Create, read, update, and delete customer records
- **Data Persistence**: All data stored locally using localStorage

### 💼 Deal Management
- **Deal Pipeline**: Track deals through various stages
- **Deal Stages**: Prospecting → Qualification → Proposal → Negotiation → Closed Won/Lost
- **Value Tracking**: Monitor deal values and revenue
- **Customer Association**: Link deals to specific customers
- **Due Date Tracking**: Set and monitor close dates
- **Overdue Alerts**: Visual indicators for overdue deals

### ✅ Task Management
- **Task Creation**: Create tasks with titles, descriptions, and due dates
- **Priority Levels**: High, Medium, Low priority classification
- **Task Status**: Mark tasks as pending or completed
- **Related Items**: Link tasks to customers or deals
- **Filtering**: Filter tasks by status (All, Pending, Completed)
- **Overdue Detection**: Highlight overdue tasks

### 📅 Calendar & Events
- **Interactive Calendar**: Monthly view with event display
- **Event Management**: Schedule and manage events
- **Date Navigation**: Navigate between months
- **Event Details**: Store event descriptions and times
- **Today Highlighting**: Current date is highlighted

### 📊 Dashboard & Analytics
- **Real-time Statistics**: Live counts of customers, deals, tasks, and events
- **Recent Activity**: Display latest activities across all modules
- **Quick Actions**: Direct access to add new items
- **Data Visualization**: Visual representation of system data

### 📈 Reports
- **Data Export**: Export customer and deal data
- **Filtering Options**: Filter reports by various criteria
- **Summary Cards**: Key metrics and statistics
- **Chart Visualization**: Graphical representation of data

## 🚀 Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- No server required - runs entirely in the browser

### Installation
1. Clone or download the project files
2. Open `index.html` in your web browser
3. Register a new account or use the demo account:
   - **Username**: `demo`
   - **Password**: `demo123`

### First Time Setup
1. Navigate to the registration page
2. Create your account with:
   - Full name
   - Email address
   - Username
   - Password (minimum 6 characters)
   - Optional company name
3. You'll be automatically logged in and redirected to the dashboard

## 📁 File Structure

```
crm-system/
├── index.html          # Login page
├── register.html       # Registration page
├── dashboard.html      # Main dashboard
├── customers.html      # Customer management
├── deals.html         # Deal management
├── tasks.html         # Task management
├── calendar.html      # Calendar view
├── reports.html       # Reports and analytics
├── css/
│   └── style.css      # Main stylesheet
├── js/
│   ├── login.js       # Authentication logic
│   ├── register.js    # Registration logic
│   ├── dashboard.js   # Dashboard functionality
│   ├── customers.js   # Customer management
│   ├── deals.js       # Deal management
│   ├── tasks.js       # Task management
│   ├── calendar.js    # Calendar functionality
│   └── reports.js     # Reports functionality
└── README.md          # This file
```

## 🎯 Usage Guide

### Managing Customers
1. Navigate to **Customers** from the main menu
2. Click **"Add New Customer"** to create a new customer
3. Fill in the required information (Name and Email are mandatory)
4. Use the search box to find specific customers
5. Click **Edit** or **Delete** buttons to modify customer records

### Managing Deals
1. Go to **Deals** section
2. Click **"Add New Deal"** to create a new deal
3. Select a customer from the dropdown
4. Set the deal value, stage, and close date
5. Add a description for the deal
6. Monitor deal progress through different stages

### Managing Tasks
1. Access **Tasks** from the main menu
2. Click **"Add New Task"** to create a new task
3. Set priority level and due date
4. Optionally link to a customer or deal
5. Use checkboxes to mark tasks as completed
6. Filter tasks by status using the dropdown

### Using the Calendar
1. Navigate to **Calendar** view
2. Use arrow buttons to navigate between months
3. Click on dates to view or add events
4. Events are displayed directly on the calendar
5. Today's date is highlighted

### Dashboard Overview
- View real-time statistics
- See recent activities
- Access quick actions for common tasks
- Monitor system health and activity

## 🔧 Technical Features

### Data Persistence
- All data is stored in browser's localStorage
- Data persists between browser sessions
- No external database required
- Automatic data backup and recovery

### Security Features
- Password validation and confirmation
- Session management
- Authentication checks on all pages
- Input validation and sanitization

### Responsive Design
- Mobile-friendly interface
- Responsive layout for all screen sizes
- Touch-friendly controls
- Optimized for desktop and mobile use

### Modern UI/UX
- Clean, professional design
- Intuitive navigation
- Color-coded status indicators
- Smooth animations and transitions
- Consistent styling throughout

## 🎨 Customization

### Styling
- Modify `css/style.css` to change colors and styling
- CSS variables are used for consistent theming
- Easy to customize colors, fonts, and layout

### Functionality
- JavaScript files are modular and well-commented
- Easy to extend with additional features
- Add new modules by following existing patterns

## 🔒 Security Notes

### For Production Use
- This is a demo system using localStorage
- For production, implement:
  - Server-side authentication
  - Database storage
  - HTTPS encryption
  - Input sanitization
  - CSRF protection
  - Rate limiting

### Data Privacy
- Data is stored locally in the browser
- Clear browser data to remove all information
- No data is transmitted to external servers

## 🐛 Troubleshooting

### Common Issues
1. **Data not saving**: Check if localStorage is enabled in your browser
2. **Login issues**: Try clearing browser cache and cookies
3. **Styling problems**: Ensure CSS file is loading correctly
4. **JavaScript errors**: Check browser console for error messages

### Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 📝 License

This project is open source and available under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For questions or issues:
1. Check the troubleshooting section
2. Review browser console for errors
3. Ensure all files are in the correct directory structure
4. Verify that JavaScript is enabled in your browser

---

**Built with ❤️ using HTML, CSS, and JavaScript**

*This CRM system provides a solid foundation for customer relationship management with modern features and intuitive design.* 
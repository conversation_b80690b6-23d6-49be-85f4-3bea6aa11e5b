<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM System - Login</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/bootstrap-custom.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            width: 100%;
            max-width: 420px;
            padding: 2rem;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 1rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            padding: 2.5rem;
        }
        .brand-logo {
            width: 60px;
            height: 60px;
            background: var(--primary-color);
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 1.5rem;
        }
        .demo-accounts {
            background: var(--bg-secondary);
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        .account-item {
            background: white;
            border-radius: 0.375rem;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            border: 1px solid var(--border-color);
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .account-item:hover {
            border-color: var(--primary-color);
            transform: translateX(4px);
        }
        .account-item:last-child {
            margin-bottom: 0;
        }
        .form-floating label {
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="brand-logo">
                <i class="bi bi-graph-up"></i>
            </div>
            <h2 class="text-center mb-1">Welcome Back</h2>
            <p class="text-center text-muted mb-4">Sign in to your CRM account</p>
            
            <div id="messageArea"></div>
            
            <form id="loginForm" class="needs-validation" novalidate>
                <div class="form-floating mb-3">
                    <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
                    <label for="email">Email address</label>
                    <div class="invalid-feedback">
                        Please enter a valid email address.
                    </div>
                </div>
                
                <div class="form-floating mb-3">
                    <input type="password" class="form-control" id="password" name="password" placeholder="Password" required>
                    <label for="password">Password</label>
                    <div class="invalid-feedback">
                        Please enter your password.
                    </div>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="rememberMe">
                        <label class="form-check-label" for="rememberMe">
                            Remember me
                        </label>
                    </div>
                    <a href="#" class="text-decoration-none">Forgot password?</a>
                </div>
                
                <button type="submit" class="btn btn-primary w-100 py-2">
                    <span class="login-text">Sign In</span>
                    <span class="spinner-border spinner-border-sm ms-2 d-none" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </span>
                </button>
            </form>
            
            <div class="text-center mt-3">
                <span class="text-muted">Don't have an account?</span>
                <a href="register.html" class="text-decoration-none ms-1">Create one</a>
            </div>
            
            <!-- Demo Account Info -->
            <div class="demo-accounts">
                <h6 class="text-center mb-3">
                    <i class="bi bi-info-circle me-1"></i>
                    Demo Accounts
                </h6>
                <div class="account-item" onclick="fillLoginForm('<EMAIL>', 'admin123')">
                    <i class="bi bi-shield-lock text-primary me-2"></i>
                    <strong>Administrator:</strong> <EMAIL> / admin123
                </div>
                <div class="account-item" onclick="fillLoginForm('<EMAIL>', 'manager123')">
                    <i class="bi bi-person-badge text-info me-2"></i>
                    <strong>Manager:</strong> <EMAIL> / manager123
                </div>
                <div class="account-item" onclick="fillLoginForm('<EMAIL>', 'user123')">
                    <i class="bi bi-person text-success me-2"></i>
                    <strong>User:</strong> <EMAIL> / user123
                </div>
                <div class="account-item" onclick="fillLoginForm('<EMAIL>', 'viewer123')">
                    <i class="bi bi-eye text-secondary me-2"></i>
                    <strong>Viewer:</strong> <EMAIL> / viewer123
                </div>
            </div>
        </div>
    </div>
    <!-- Bootstrap 5 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom Scripts -->
    <script src="js/common-ui.js"></script>
    <script src="js/validation.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/login.js"></script>
    <script>
        // Helper function to fill login form with demo credentials
        function fillLoginForm(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
            document.getElementById('email').classList.add('is-valid');
            document.getElementById('password').classList.add('is-valid');
        }
        
        // Bootstrap form validation
        (function() {
            'use strict';
            const forms = document.querySelectorAll('.needs-validation');
            Array.from(forms).forEach(form => {
                form.addEventListener('submit', event => {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        })();
    </script>
</body>
</html>

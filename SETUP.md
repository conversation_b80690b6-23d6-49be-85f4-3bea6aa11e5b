# CRM System Setup Instructions

## Quick Start Guide

### 1. Prerequisites
- Install Node.js (v14 or higher): https://nodejs.org/
- Install PostgreSQL: https://www.postgresql.org/download/

### 2. Database Setup
1. Open PostgreSQL command line or pgAdmin
2. Create a new database:
   ```sql
   CREATE DATABASE crm_database;
   ```

### 3. Backend Setup
1. Navigate to backend directory:
   ```bash
   cd backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Configure environment:
   - Copy `.env.example` to `.env`
   - Update database credentials in `.env`:
     ```
     DB_USER=your_postgres_username
     DB_PASSWORD=your_postgres_password
     ```

4. Start the backend server:
   ```bash
   npm start
   ```
   The backend will run on http://localhost:5000

### 4. Frontend Setup
1. Go back to main directory:
   ```bash
   cd ..
   ```

2. Start a local web server:
   
   **Option 1 - Using Python:**
   ```bash
   python -m http.server 5500
   ```
   
   **Option 2 - Using Node.js:**
   ```bash
   npx http-server -p 5500
   ```
   
   **Option 3 - Using VS Code:**
   - Install "Live Server" extension
   - Right-click on `index.html`
   - Select "Open with Live Server"

3. Open browser and navigate to:
   ```
   http://localhost:5500
   ```

## Demo Accounts

You can use these accounts for testing:

| Role | Email | Password |
|------|-------|----------|
| Admin | <EMAIL> | admin123 |
| Manager | <EMAIL> | manager123 |
| User | <EMAIL> | user123 |
| Viewer | <EMAIL> | viewer123 |

## Features

### Backend API
- ✅ RESTful API with Express.js
- ✅ PostgreSQL database with Sequelize ORM
- ✅ JWT authentication
- ✅ Role-based access control
- ✅ Complete CRUD operations
- ✅ Data validation
- ✅ Security features (helmet, rate limiting, CORS)

### Frontend
- ✅ Modern, responsive UI
- ✅ API integration
- ✅ Real-time data updates
- ✅ Dashboard with statistics
- ✅ Customer management
- ✅ Contact tracking
- ✅ Deal pipeline
- ✅ Task management
- ✅ Calendar integration
- ✅ Email interface
- ✅ Reports and analytics

## Troubleshooting

### Backend won't start
- Check if PostgreSQL is running
- Verify database credentials in `.env`
- Make sure port 5000 is not in use

### Can't login
- Ensure backend is running
- Check browser console for errors
- Try clearing browser cache/localStorage

### Database connection failed
- Verify PostgreSQL is installed and running
- Check database name exists
- Confirm credentials are correct

## Need Help?
- Check the full README.md for detailed documentation
- Look at browser console for error messages
- Ensure all services are running

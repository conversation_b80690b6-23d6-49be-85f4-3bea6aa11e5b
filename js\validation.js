/**
 * Frontend Validation Utilities
 * 
 * Provides comprehensive validation for user input on the frontend
 * to ensure data integrity and prevent malicious input
 */

// XSS Prevention: Escape HTML entities
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, m => map[m]);
}

// Remove potentially dangerous HTML tags
function sanitizeHtml(html) {
    // Create a temporary element to parse HTML
    const temp = document.createElement('div');
    temp.textContent = html;
    return temp.innerHTML;
}

// Input sanitization for different field types
const sanitizers = {
    text: (value) => {
        return escapeHtml(String(value).trim());
    },
    
    email: (value) => {
        return String(value).trim().toLowerCase();
    },
    
    phone: (value) => {
        return String(value).replace(/[^\d\s+()-]/g, '').trim();
    },
    
    number: (value) => {
        return String(value).replace(/[^\d.-]/g, '').trim();
    },
    
    alphanumeric: (value) => {
        return String(value).replace(/[^a-zA-Z0-9]/g, '').trim();
    },
    
    url: (value) => {
        try {
            const url = new URL(value);
            return url.href;
        } catch {
            return '';
        }
    }
};

// Validation rules
const validators = {
    required: (value) => {
        return value !== null && value !== undefined && value.toString().trim() !== '';
    },
    
    email: (value) => {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(String(value).toLowerCase());
    },
    
    phone: (value) => {
        const re = /^[\d\s+()-]{10,}$/;
        return re.test(String(value));
    },
    
    url: (value) => {
        try {
            new URL(value);
            return true;
        } catch {
            return false;
        }
    },
    
    minLength: (value, min) => {
        return String(value).length >= min;
    },
    
    maxLength: (value, max) => {
        return String(value).length <= max;
    },
    
    min: (value, min) => {
        return parseFloat(value) >= min;
    },
    
    max: (value, max) => {
        return parseFloat(value) <= max;
    },
    
    pattern: (value, pattern) => {
        const re = new RegExp(pattern);
        return re.test(String(value));
    },
    
    strongPassword: (value) => {
        // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character
        const re = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
        return re.test(String(value));
    },
    
    creditCard: (value) => {
        // Basic credit card validation (Luhn algorithm)
        const cleaned = String(value).replace(/\s/g, '');
        if (!/^\d{13,19}$/.test(cleaned)) return false;
        
        let sum = 0;
        let isEven = false;
        
        for (let i = cleaned.length - 1; i >= 0; i--) {
            let digit = parseInt(cleaned[i]);
            
            if (isEven) {
                digit *= 2;
                if (digit > 9) {
                    digit -= 9;
                }
            }
            
            sum += digit;
            isEven = !isEven;
        }
        
        return sum % 10 === 0;
    },
    
    date: (value) => {
        const date = new Date(value);
        return date instanceof Date && !isNaN(date);
    },
    
    futureDate: (value) => {
        const date = new Date(value);
        return date instanceof Date && !isNaN(date) && date > new Date();
    },
    
    pastDate: (value) => {
        const date = new Date(value);
        return date instanceof Date && !isNaN(date) && date < new Date();
    }
};

// Validation schemas for different forms
const validationSchemas = {
    login: {
        email: {
            required: true,
            type: 'email',
            validators: ['required', 'email']
        },
        password: {
            required: true,
            validators: ['required', { name: 'minLength', params: 6 }]
        }
    },
    
    register: {
        fullName: {
            required: true,
            type: 'text',
            validators: ['required', { name: 'minLength', params: 2 }, { name: 'maxLength', params: 100 }]
        },
        email: {
            required: true,
            type: 'email',
            validators: ['required', 'email']
        },
        password: {
            required: true,
            validators: ['required', 'strongPassword']
        },
        confirmPassword: {
            required: true,
            validators: ['required', { name: 'matches', field: 'password' }]
        }
    },
    
    customer: {
        name: {
            required: true,
            type: 'text',
            validators: ['required', { name: 'maxLength', params: 100 }]
        },
        email: {
            required: true,
            type: 'email',
            validators: ['required', 'email']
        },
        phone: {
            type: 'phone',
            validators: ['phone']
        },
        company: {
            type: 'text',
            validators: [{ name: 'maxLength', params: 100 }]
        },
        address: {
            type: 'text',
            validators: [{ name: 'maxLength', params: 500 }]
        }
    },
    
    deal: {
        title: {
            required: true,
            type: 'text',
            validators: ['required', { name: 'maxLength', params: 200 }]
        },
        value: {
            required: true,
            type: 'number',
            validators: ['required', { name: 'min', params: 0 }]
        },
        probability: {
            type: 'number',
            validators: [{ name: 'min', params: 0 }, { name: 'max', params: 100 }]
        },
        expectedCloseDate: {
            type: 'date',
            validators: ['date', 'futureDate']
        }
    },
    
    task: {
        title: {
            required: true,
            type: 'text',
            validators: ['required', { name: 'maxLength', params: 200 }]
        },
        description: {
            type: 'text',
            validators: [{ name: 'maxLength', params: 1000 }]
        },
        dueDate: {
            type: 'date',
            validators: ['date']
        },
        priority: {
            required: true,
            validators: ['required', { name: 'pattern', params: '^(low|medium|high|urgent)$' }]
        }
    }
};

// Main validation function
function validate(data, schema) {
    const errors = {};
    
    for (const field in schema) {
        const fieldSchema = schema[field];
        const value = data[field];
        
        // Check required
        if (fieldSchema.required && !validators.required(value)) {
            errors[field] = `${field} is required`;
            continue;
        }
        
        // Skip validation if field is empty and not required
        if (!fieldSchema.required && !value) {
            continue;
        }
        
        // Apply sanitization
        if (fieldSchema.type && sanitizers[fieldSchema.type]) {
            data[field] = sanitizers[fieldSchema.type](value);
        }
        
        // Run validators
        for (const validator of fieldSchema.validators || []) {
            let validatorName, params;
            
            if (typeof validator === 'string') {
                validatorName = validator;
            } else {
                validatorName = validator.name;
                params = validator.params;
            }
            
            // Special case for matches validator
            if (validatorName === 'matches') {
                if (value !== data[validator.field]) {
                    errors[field] = `${field} must match ${validator.field}`;
                    break;
                }
                continue;
            }
            
            const validatorFn = validators[validatorName];
            if (validatorFn) {
                const isValid = params !== undefined 
                    ? validatorFn(value, params)
                    : validatorFn(value);
                
                if (!isValid) {
                    errors[field] = getErrorMessage(field, validatorName, params);
                    break;
                }
            }
        }
    }
    
    return {
        isValid: Object.keys(errors).length === 0,
        errors,
        data
    };
}

// Error messages
function getErrorMessage(field, validator, params) {
    const messages = {
        required: `${field} is required`,
        email: `${field} must be a valid email address`,
        phone: `${field} must be a valid phone number`,
        url: `${field} must be a valid URL`,
        minLength: `${field} must be at least ${params} characters`,
        maxLength: `${field} must not exceed ${params} characters`,
        min: `${field} must be at least ${params}`,
        max: `${field} must not exceed ${params}`,
        pattern: `${field} is invalid`,
        strongPassword: `${field} must be at least 8 characters with uppercase, lowercase, number, and special character`,
        creditCard: `${field} must be a valid credit card number`,
        date: `${field} must be a valid date`,
        futureDate: `${field} must be a future date`,
        pastDate: `${field} must be a past date`
    };
    
    return messages[validator] || `${field} is invalid`;
}

// Form validation helper
function validateForm(formElement, schema) {
    const formData = new FormData(formElement);
    const data = {};
    
    // Convert FormData to object
    for (const [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    return validate(data, schema);
}

// Real-time validation helper
function setupRealtimeValidation(formElement, schema) {
    const inputs = formElement.querySelectorAll('input, textarea, select');
    
    inputs.forEach(input => {
        const fieldName = input.name;
        const fieldSchema = schema[fieldName];
        
        if (!fieldSchema) return;
        
        // Validate on blur
        input.addEventListener('blur', () => {
            const result = validate({ [fieldName]: input.value }, { [fieldName]: fieldSchema });
            
            if (result.isValid) {
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
                
                const feedback = input.nextElementSibling;
                if (feedback && feedback.classList.contains('invalid-feedback')) {
                    feedback.textContent = '';
                }
            } else {
                input.classList.remove('is-valid');
                input.classList.add('is-invalid');
                
                const feedback = input.nextElementSibling;
                if (feedback && feedback.classList.contains('invalid-feedback')) {
                    feedback.textContent = result.errors[fieldName];
                }
            }
        });
        
        // Clear validation on input
        input.addEventListener('input', () => {
            input.classList.remove('is-invalid', 'is-valid');
        });
    });
}

// CSRF token handling
function getCSRFToken() {
    // Try to get from cookie
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrf-token') {
            return value;
        }
    }
    
    // Try to get from meta tag
    const metaTag = document.querySelector('meta[name="csrf-token"]');
    if (metaTag) {
        return metaTag.content;
    }
    
    return null;
}

// Add CSRF token to requests
function addCSRFToken(headers = {}) {
    const token = getCSRFToken();
    if (token) {
        headers['X-CSRF-Token'] = token;
    }
    return headers;
}

// Safe JSON parse
function safeJSONParse(text) {
    try {
        return JSON.parse(text);
    } catch (e) {
        console.error('JSON parse error:', e);
        return null;
    }
}

// Debounce function for input validation
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        escapeHtml,
        sanitizeHtml,
        sanitizers,
        validators,
        validationSchemas,
        validate,
        validateForm,
        setupRealtimeValidation,
        getCSRFToken,
        addCSRFToken,
        safeJSONParse,
        debounce
    };
}

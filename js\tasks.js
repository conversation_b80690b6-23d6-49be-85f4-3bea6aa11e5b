// Tasks functionality
document.addEventListener('DOMContentLoaded', function() {
    // Logout functionality
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function() {
            auth.logout();
        });
    }
    
    // Initialize tasks data
    let tasks = JSON.parse(localStorage.getItem('tasks') || '[]');
    
    // Add sample data if empty
    if (tasks.length === 0) {
        tasks = [
            { id: 1, title: 'Follow up with leads', description: 'Contact potential customers from last week', priority: 'High', dueDate: '2024-03-20', completed: false, relatedTo: 'customer', relatedId: '<PERSON>', createdAt: new Date().toISOString() },
            { id: 2, title: 'Prepare presentation', description: 'Create slides for client meeting', priority: 'Medium', dueDate: '2024-03-25', completed: false, relatedTo: 'deal', relatedId: 'ABC Corp Contract', createdAt: new Date().toISOString() },
            { id: 3, title: 'Review contracts', description: 'Review and update service agreements', priority: 'Low', dueDate: '2024-03-30', completed: true, completedAt: new Date().toISOString(), createdAt: new Date().toISOString() }
        ];
        localStorage.setItem('tasks', JSON.stringify(tasks));
    }
    
    // Form elements
    const addTaskBtn = document.getElementById('addTaskBtn');
    const taskFormContainer = document.getElementById('taskFormContainer');
    const taskForm = document.getElementById('taskForm');
    const formTitle = document.getElementById('formTitle');
    const cancelTaskBtn = document.getElementById('cancelTaskBtn');
    const taskFilter = document.getElementById('taskFilter');
    const pendingTasksList = document.getElementById('pendingTasksList');
    const completedTasksList = document.getElementById('completedTasksList');
    const taskRelatedTo = document.getElementById('taskRelatedTo');
    const customerSelectGroup = document.getElementById('customerSelectGroup');
    const dealSelectGroup = document.getElementById('dealSelectGroup');
    const taskCustomer = document.getElementById('taskCustomer');
    const taskDeal = document.getElementById('taskDeal');
    
    // Load related data for dropdowns
    function loadRelatedData() {
        const customers = JSON.parse(localStorage.getItem('customers') || '[]');
        const deals = JSON.parse(localStorage.getItem('deals') || '[]');
        
        // Load customers
        taskCustomer.innerHTML = '<option value="">Select Customer</option>';
        customers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.name;
            option.textContent = customer.name;
            taskCustomer.appendChild(option);
        });
        
        // Load deals
        taskDeal.innerHTML = '<option value="">Select Deal</option>';
        deals.forEach(deal => {
            const option = document.createElement('option');
            option.value = deal.title;
            option.textContent = deal.title;
            taskDeal.appendChild(option);
        });
    }
    
    // Handle related to selection
    if (taskRelatedTo) {
        taskRelatedTo.addEventListener('change', function() {
            customerSelectGroup.style.display = 'none';
            dealSelectGroup.style.display = 'none';
            
            if (this.value === 'customer') {
                customerSelectGroup.style.display = 'block';
            } else if (this.value === 'deal') {
                dealSelectGroup.style.display = 'block';
            }
        });
    }
    
    // Display tasks
    function displayTasks() {
        const pendingTasks = tasks.filter(task => !task.completed);
        const completedTasks = tasks.filter(task => task.completed);
        
        // Display pending tasks
        pendingTasksList.innerHTML = '';
        pendingTasks.forEach(task => {
            const li = document.createElement('li');
            const dueDate = new Date(task.dueDate);
            const isOverdue = dueDate < new Date();
            
            li.innerHTML = `
                <div class="task-content">
                    <div class="task-title">
                        <input type="checkbox" onchange="toggleTask(${task.id})">
                        <span class="${isOverdue ? 'overdue' : ''}">${task.title}</span>
                    </div>
                    <div class="task-meta">
                        <span class="priority-${task.priority.toLowerCase()}">${task.priority}</span>
                        <span class="due-date">Due: ${task.dueDate}</span>
                        ${task.relatedTo ? `<span class="related-to">${task.relatedTo}: ${task.relatedId}</span>` : ''}
                    </div>
                    <div class="task-description">${task.description}</div>
                    <div class="task-actions">
                        <button class="btn-primary" onclick="editTask(${task.id})">Edit</button>
                        <button class="btn-secondary" onclick="deleteTask(${task.id})">Delete</button>
                    </div>
                </div>
            `;
            pendingTasksList.appendChild(li);
        });
        
        // Display completed tasks
        completedTasksList.innerHTML = '';
        completedTasks.forEach(task => {
            const li = document.createElement('li');
            li.className = 'task-completed';
            li.innerHTML = `
                <div class="task-content">
                    <div class="task-title">
                        <input type="checkbox" checked disabled>
                        <span class="completed">${task.title}</span>
                    </div>
                    <div class="task-meta">
                        <span class="priority-${task.priority.toLowerCase()}">${task.priority}</span>
                        <span class="completed-date">Completed: ${new Date(task.completedAt).toLocaleDateString()}</span>
                        ${task.relatedTo ? `<span class="related-to">${task.relatedTo}: ${task.relatedId}</span>` : ''}
                    </div>
                    <div class="task-description">${task.description}</div>
                    <div class="task-actions">
                        <button class="btn-secondary" onclick="deleteTask(${task.id})">Delete</button>
                    </div>
                </div>
            `;
            completedTasksList.appendChild(li);
        });
    }
    
    // Show form
    function showForm(mode = 'add', task = null) {
        taskFormContainer.style.display = 'block';
        loadRelatedData();
        
        if (mode === 'add') {
            formTitle.textContent = 'Add New Task';
            taskForm.reset();
            document.getElementById('taskId').value = '';
            customerSelectGroup.style.display = 'none';
            dealSelectGroup.style.display = 'none';
        } else {
            formTitle.textContent = 'Edit Task';
            document.getElementById('taskId').value = task.id;
            document.getElementById('taskTitle').value = task.title;
            document.getElementById('taskDescription').value = task.description || '';
            document.getElementById('taskDueDate').value = task.dueDate;
            document.getElementById('taskPriority').value = task.priority;
            document.getElementById('taskRelatedTo').value = task.relatedTo || '';
            
            if (task.relatedTo === 'customer') {
                customerSelectGroup.style.display = 'block';
                document.getElementById('taskCustomer').value = task.relatedId;
            } else if (task.relatedTo === 'deal') {
                dealSelectGroup.style.display = 'block';
                document.getElementById('taskDeal').value = task.relatedId;
            }
        }
    }
    
    // Hide form
    function hideForm() {
        taskFormContainer.style.display = 'none';
        taskForm.reset();
        customerSelectGroup.style.display = 'none';
        dealSelectGroup.style.display = 'none';
    }
    
    // Add task button
    if (addTaskBtn) {
        addTaskBtn.addEventListener('click', () => showForm('add'));
    }
    
    // Cancel button
    if (cancelTaskBtn) {
        cancelTaskBtn.addEventListener('click', hideForm);
    }
    
    // Form submission
    if (taskForm) {
        taskForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const taskId = document.getElementById('taskId').value;
            const taskData = {
                title: document.getElementById('taskTitle').value.trim(),
                description: document.getElementById('taskDescription').value.trim(),
                dueDate: document.getElementById('taskDueDate').value,
                priority: document.getElementById('taskPriority').value,
                relatedTo: document.getElementById('taskRelatedTo').value,
                relatedId: document.getElementById('taskRelatedTo').value === 'customer' ? 
                    document.getElementById('taskCustomer').value : 
                    document.getElementById('taskRelatedTo').value === 'deal' ? 
                    document.getElementById('taskDeal').value : '',
                updatedAt: new Date().toISOString()
            };
            
            if (!taskData.title || !taskData.dueDate) {
                alert('Title and due date are required!');
                return;
            }
            
            if (taskId) {
                // Edit existing task
                const index = tasks.findIndex(t => t.id == taskId);
                if (index !== -1) {
                    tasks[index] = { ...tasks[index], ...taskData };
                }
            } else {
                // Add new task
                taskData.id = Date.now();
                taskData.completed = false;
                taskData.createdAt = new Date().toISOString();
                tasks.push(taskData);
            }
            
            localStorage.setItem('tasks', JSON.stringify(tasks));
            displayTasks();
            hideForm();
            showMessage('Task saved successfully!', 'success');
        });
    }
    
    // Filter functionality
    if (taskFilter) {
        taskFilter.addEventListener('change', function() {
            const filterValue = this.value;
            const pendingTasks = document.querySelectorAll('#pendingTasksList li');
            const completedTasks = document.querySelectorAll('#completedTasksList li');
            
            if (filterValue === 'all') {
                pendingTasks.forEach(li => li.style.display = '');
                completedTasks.forEach(li => li.style.display = '');
            } else if (filterValue === 'pending') {
                pendingTasks.forEach(li => li.style.display = '');
                completedTasks.forEach(li => li.style.display = 'none');
            } else if (filterValue === 'completed') {
                pendingTasks.forEach(li => li.style.display = 'none');
                completedTasks.forEach(li => li.style.display = '');
            }
        });
    }
    
    // Global functions
    window.toggleTask = function(id) {
        const task = tasks.find(t => t.id == id);
        if (task) {
            task.completed = !task.completed;
            if (task.completed) {
                task.completedAt = new Date().toISOString();
            } else {
                delete task.completedAt;
            }
            localStorage.setItem('tasks', JSON.stringify(tasks));
            displayTasks();
            showMessage(task.completed ? 'Task completed!' : 'Task marked as pending', 'success');
        }
    };
    
    window.editTask = function(id) {
        const task = tasks.find(t => t.id == id);
        if (task) {
            showForm('edit', task);
        }
    };
    
    window.deleteTask = function(id) {
        if (confirm('Are you sure you want to delete this task?')) {
            tasks = tasks.filter(t => t.id != id);
            localStorage.setItem('tasks', JSON.stringify(tasks));
            displayTasks();
            showMessage('Task deleted successfully!', 'success');
        }
    };
    
    // Initial display
    displayTasks();
});

function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: bold;
        z-index: 1000;
        ${type === 'error' ? 'background-color: #e74c3c;' : 'background-color: #27ae60;'}
    `;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 3000);
}
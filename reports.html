<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM System - Reports</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/modern-ui.css">
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1>CRM System</h1>
            </div>
            <div class="user-info">
                <span id="username">User</span>
                <span id="userRole" class="user-role">Role</span>
                <button id="logoutBtn">Logout</button>
            </div>
        </header>
        
        <nav>
            <ul>
                <li><a href="dashboard.html">Dashboard</a></li>
                <li><a href="customers.html">Customers</a></li>
                <li><a href="contacts.html">Contacts</a></li>
                <li><a href="deals.html">Deals</a></li>
                <li><a href="tasks.html">Tasks</a></li>
                <li><a href="calendar.html">Calendar</a></li>
                <li><a href="reports.html" class="active">Reports</a></li>
                <li><a href="users.html" class="admin-only">Users</a></li>
                <li><a href="email.html">Email</a></li>
            </ul>
        </nav>
        
        <main>
            <h2>Reports</h2>
            
            <div class="reports-filter">
                <form id="reportsFilter">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="startDate">Start Date</label>
                            <input type="date" id="startDate" name="startDate">
                        </div>
                        <div class="form-group">
                            <label for="endDate">End Date</label>
                            <input type="date" id="endDate" name="endDate">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="reportType">Report Type</label>
                            <select id="reportType">
                                <option value="sales">Sales Report</option>
                                <option value="customer">Customer Report</option>
                                <option value="activity">Activity Report</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="reportPeriod">Period</label>
                            <select id="reportPeriod">
                                <option value="week">This Week</option>
                                <option value="month">This Month</option>
                                <option value="quarter">This Quarter</option>
                                <option value="year">This Year</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">Generate Report</button>
                        <button type="button" id="exportData" class="btn-secondary">Export Data</button>
                    </div>
                </form>
            </div>
            
            <div class="report-content">
                <div class="report-summary">
                    <h3>Report Summary</h3>
                    <div class="summary-cards">
                        <div class="summary-card">
                            <h4>Total Revenue</h4>
                            <p id="totalRevenue">$0</p>
                        </div>
                        <div class="summary-card">
                            <h4>New Customers</h4>
                            <p id="newCustomers">0</p>
                        </div>
                        <div class="summary-card">
                            <h4>Deals Closed</h4>
                            <p id="dealsClosed">0</p>
                        </div>
                        <div class="summary-card">
                            <h4>Tasks Completed</h4>
                            <p id="tasksCompleted">0</p>
                        </div>
                    </div>
                </div>
                
                <div class="report-charts">
                    <div class="chart-container">
                        <h4>Revenue Trend</h4>
                        <canvas id="revenueChart"></canvas>
                    </div>
                    
                    <div class="chart-container">
                        <h4>Deal Stages</h4>
                        <canvas id="dealStageChart"></canvas>
                    </div>
                </div>
                
                <div class="report-details">
                    <h3>Details</h3>
                    <div class="table-container">
                        <table id="reportDetailsTable">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Description</th>
                                    <th>Value</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="reportDetailsTableBody">
                                <!-- Report details will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/reports.js"></script>
</body>
</html>
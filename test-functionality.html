<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM System - Functionality Test</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/bootstrap-custom.css">
</head>
<body>
    <div class="container mt-4">
        <h1>CRM System - Functionality Test</h1>
        <p class="text-muted">This page tests the core functionality of the CRM system.</p>
        
        <div id="testResults" class="mt-4"></div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Actions</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2" onclick="testCustomers()">Test Customers</button><br>
                        <button class="btn btn-success mb-2" onclick="testContacts()">Test Contacts</button><br>
                        <button class="btn btn-warning mb-2" onclick="testTasks()">Test Tasks</button><br>
                        <button class="btn btn-info mb-2" onclick="testDeals()">Test Deals</button><br>
                        <button class="btn btn-secondary mb-2" onclick="clearAllData()">Clear All Data</button><br>
                        <button class="btn btn-danger mb-2" onclick="populateSampleData()">Populate Sample Data</button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Navigation Test</h5>
                    </div>
                    <div class="card-body">
                        <a href="dashboard.html" class="btn btn-outline-primary mb-2">Dashboard</a><br>
                        <a href="customers.html" class="btn btn-outline-primary mb-2">Customers</a><br>
                        <a href="contacts.html" class="btn btn-outline-primary mb-2">Contacts</a><br>
                        <a href="deals.html" class="btn btn-outline-primary mb-2">Deals</a><br>
                        <a href="tasks.html" class="btn btn-outline-primary mb-2">Tasks</a><br>
                        <a href="calendar.html" class="btn btn-outline-primary mb-2">Calendar</a><br>
                        <a href="reports.html" class="btn btn-outline-primary mb-2">Reports</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function showResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            resultsDiv.appendChild(alert);
        }

        function testCustomers() {
            try {
                const customers = JSON.parse(localStorage.getItem('customers') || '[]');
                showResult(`Customers test passed. Found ${customers.length} customers in storage.`, 'success');
            } catch (error) {
                showResult(`Customers test failed: ${error.message}`, 'danger');
            }
        }

        function testContacts() {
            try {
                const contacts = JSON.parse(localStorage.getItem('contacts') || '[]');
                showResult(`Contacts test passed. Found ${contacts.length} contacts in storage.`, 'success');
            } catch (error) {
                showResult(`Contacts test failed: ${error.message}`, 'danger');
            }
        }

        function testTasks() {
            try {
                const tasks = JSON.parse(localStorage.getItem('tasks') || '[]');
                showResult(`Tasks test passed. Found ${tasks.length} tasks in storage.`, 'success');
            } catch (error) {
                showResult(`Tasks test failed: ${error.message}`, 'danger');
            }
        }

        function testDeals() {
            try {
                const deals = JSON.parse(localStorage.getItem('deals') || '[]');
                showResult(`Deals test passed. Found ${deals.length} deals in storage.`, 'success');
            } catch (error) {
                showResult(`Deals test failed: ${error.message}`, 'danger');
            }
        }

        function clearAllData() {
            if (confirm('Are you sure you want to clear all data? This cannot be undone.')) {
                localStorage.removeItem('customers');
                localStorage.removeItem('contacts');
                localStorage.removeItem('tasks');
                localStorage.removeItem('deals');
                localStorage.removeItem('events');
                showResult('All data cleared successfully!', 'warning');
            }
        }

        function populateSampleData() {
            // Sample customers
            const sampleCustomers = [
                {
                    id: 1,
                    name: 'Acme Corporation',
                    email: '<EMAIL>',
                    phone: '******-0123',
                    address: '123 Business St, City, State 12345',
                    status: 'active',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 2,
                    name: 'TechStart Inc',
                    email: '<EMAIL>',
                    phone: '******-0456',
                    address: '456 Innovation Ave, Tech City, TC 67890',
                    status: 'active',
                    createdAt: new Date().toISOString()
                }
            ];

            // Sample contacts
            const sampleContacts = [
                {
                    id: 1,
                    name: 'John Smith',
                    email: '<EMAIL>',
                    phone: '******-0124',
                    customer: 'Acme Corporation',
                    position: 'CEO',
                    notes: 'Primary decision maker',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 2,
                    name: 'Sarah Johnson',
                    email: '<EMAIL>',
                    phone: '******-0457',
                    customer: 'TechStart Inc',
                    position: 'CTO',
                    notes: 'Technical contact',
                    createdAt: new Date().toISOString()
                }
            ];

            // Sample tasks
            const sampleTasks = [
                {
                    id: 1,
                    title: 'Follow up with Acme Corp',
                    description: 'Schedule a meeting to discuss their requirements',
                    dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    priority: 'High',
                    status: 'pending',
                    relatedTo: 'customer',
                    relatedId: 'Acme Corporation',
                    completed: false,
                    createdAt: new Date().toISOString()
                },
                {
                    id: 2,
                    title: 'Prepare proposal for TechStart',
                    description: 'Create detailed proposal for their software needs',
                    dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    priority: 'Medium',
                    status: 'in-progress',
                    relatedTo: 'customer',
                    relatedId: 'TechStart Inc',
                    completed: false,
                    createdAt: new Date().toISOString()
                }
            ];

            // Sample deals
            const sampleDeals = [
                {
                    id: 1,
                    title: 'Acme Corp Software License',
                    customer: 'Acme Corporation',
                    value: 50000,
                    stage: 'negotiation',
                    status: 'active',
                    probability: 75,
                    expectedCloseDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    description: 'Enterprise software licensing deal',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 2,
                    title: 'TechStart Consulting Services',
                    customer: 'TechStart Inc',
                    value: 25000,
                    stage: 'proposal',
                    status: 'active',
                    probability: 60,
                    expectedCloseDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    description: 'Technical consulting and implementation services',
                    createdAt: new Date().toISOString()
                }
            ];

            // Save to localStorage
            localStorage.setItem('customers', JSON.stringify(sampleCustomers));
            localStorage.setItem('contacts', JSON.stringify(sampleContacts));
            localStorage.setItem('tasks', JSON.stringify(sampleTasks));
            localStorage.setItem('deals', JSON.stringify(sampleDeals));

            showResult('Sample data populated successfully! You can now test all features.', 'success');
        }

        // Run initial tests
        document.addEventListener('DOMContentLoaded', function() {
            showResult('CRM System functionality test page loaded successfully!', 'info');
        });
    </script>
</body>
</html>

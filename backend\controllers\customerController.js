const { Customer, Contact, Deal, Task, Activity, User, sequelize } = require('../models');
const { Op } = require('sequelize');

// @desc    Get all customers
// @route   GET /api/customers
// @access  Private
exports.getCustomers = async (req, res, next) => {
  try {
    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 25;
    const offset = (page - 1) * limit;

    // Filtering
    const where = {};
    
    if (req.query.status) {
      where.status = req.query.status;
    }
    
    if (req.query.rating) {
      where.rating = req.query.rating;
    }
    
    if (req.query.search) {
      where[Op.or] = [
        { companyName: { [Op.iLike]: `%${req.query.search}%` } },
        { email: { [Op.iLike]: `%${req.query.search}%` } },
        { '$contacts.firstName$': { [Op.iLike]: `%${req.query.search}%` } },
        { '$contacts.lastName$': { [Op.iLike]: `%${req.query.search}%` } }
      ];
    }

    // Sorting
    const order = [];
    if (req.query.sortBy) {
      const sortParts = req.query.sortBy.split(':');
      order.push([sortParts[0], sortParts[1] ? sortParts[1].toUpperCase() : 'ASC']);
    } else {
      order.push(['createdAt', 'DESC']);
    }

    const { count, rows } = await Customer.findAndCountAll({
      where,
      include: [
        {
          model: Contact,
          as: 'contacts',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone', 'isPrimary']
        },
        {
          model: User,
          as: 'assignedTo',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ],
      order,
      limit,
      offset,
      distinct: true
    });

    res.status(200).json({
      success: true,
      count,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(count / limit)
      },
      data: rows
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single customer
// @route   GET /api/customers/:id
// @access  Private
exports.getCustomer = async (req, res, next) => {
  try {
    const customer = await Customer.findByPk(req.params.id, {
      include: [
        {
          model: Contact,
          as: 'contacts'
        },
        {
          model: Deal,
          as: 'deals',
          include: [
            {
              model: User,
              as: 'assignedTo',
              attributes: ['id', 'firstName', 'lastName']
            }
          ]
        },
        {
          model: Task,
          as: 'tasks',
          where: { status: { [Op.ne]: 'completed' } },
          required: false,
          limit: 5,
          order: [['dueDate', 'ASC']]
        },
        {
          model: Activity,
          as: 'activities',
          limit: 10,
          order: [['activityDate', 'DESC']],
          include: [
            {
              model: User,
              as: 'performedBy',
              attributes: ['id', 'firstName', 'lastName']
            }
          ]
        },
        {
          model: User,
          as: 'assignedTo',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'firstName', 'lastName']
        }
      ]
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        error: 'Customer not found'
      });
    }

    res.status(200).json({
      success: true,
      data: customer
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create new customer
// @route   POST /api/customers
// @access  Private
exports.createCustomer = async (req, res, next) => {
  try {
    req.body.createdById = req.user.id;
    
    if (!req.body.assignedToId) {
      req.body.assignedToId = req.user.id;
    }

    const customer = await Customer.create(req.body);

    // Log activity
    await Activity.create({
      type: 'status_change',
      subject: 'New customer created',
      description: `Customer ${customer.companyName} was created`,
      entityType: 'customer',
      entityId: customer.id,
      customerId: customer.id,
      performedById: req.user.id
    });

    res.status(201).json({
      success: true,
      data: customer
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update customer
// @route   PUT /api/customers/:id
// @access  Private
exports.updateCustomer = async (req, res, next) => {
  try {
    let customer = await Customer.findByPk(req.params.id);

    if (!customer) {
      return res.status(404).json({
        success: false,
        error: 'Customer not found'
      });
    }

    // Track status changes
    const oldStatus = customer.status;
    
    customer = await customer.update(req.body);

    // Log status change activity
    if (oldStatus !== customer.status) {
      await Activity.create({
        type: 'status_change',
        subject: 'Status changed',
        description: `Status changed from ${oldStatus} to ${customer.status}`,
        entityType: 'customer',
        entityId: customer.id,
        customerId: customer.id,
        performedById: req.user.id
      });
    }

    res.status(200).json({
      success: true,
      data: customer
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete customer
// @route   DELETE /api/customers/:id
// @access  Private (Admin only)
exports.deleteCustomer = async (req, res, next) => {
  try {
    const customer = await Customer.findByPk(req.params.id);

    if (!customer) {
      return res.status(404).json({
        success: false,
        error: 'Customer not found'
      });
    }

    await customer.destroy();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get customer statistics
// @route   GET /api/customers/stats
// @access  Private
exports.getCustomerStats = async (req, res, next) => {
  try {
    const totalCustomers = await Customer.count();
    const customersByStatus = await Customer.findAll({
      attributes: [
        'status',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['status']
    });

    const customersByRating = await Customer.findAll({
      attributes: [
        'rating',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['rating']
    });

    const recentCustomers = await Customer.findAll({
      limit: 5,
      order: [['createdAt', 'DESC']],
      include: [{
        model: User,
        as: 'assignedTo',
        attributes: ['firstName', 'lastName']
      }]
    });

    res.status(200).json({
      success: true,
      data: {
        total: totalCustomers,
        byStatus: customersByStatus,
        byRating: customersByRating,
        recent: recentCustomers
      }
    });
  } catch (error) {
    next(error);
  }
};

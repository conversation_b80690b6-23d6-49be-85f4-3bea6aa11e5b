<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM System - Dashboard</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/bootstrap-custom.css">
</head>
<body>
    <!-- Modern Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-custom sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="bi bi-graph-up me-2"></i>CRM System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarMain">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.html">
                            <i class="bi bi-speedometer2 me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="customers.html">
                            <i class="bi bi-people me-1"></i>Customers
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contacts.html">
                            <i class="bi bi-person-lines-fill me-1"></i>Contacts
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="deals.html">
                            <i class="bi bi-currency-dollar me-1"></i>Deals
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="tasks.html">
                            <i class="bi bi-check-square me-1"></i>Tasks
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="calendar.html">
                            <i class="bi bi-calendar3 me-1"></i>Calendar
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="bi bi-bar-chart me-1"></i>Reports
                        </a>
                    </li>
                </ul>
                
                <!-- User Profile Dropdown -->
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle d-flex align-items-center" type="button" data-bs-toggle="dropdown">
                        <div class="user-avatar me-2">
                            <span id="userInitial">U</span>
                        </div>
                        <div class="text-start">
                            <div class="fw-semibold" id="username">User</div>
                            <div class="small text-muted" id="userRole">Role</div>
                        </div>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="bi bi-person me-2"></i>Profile</a></li>
                        <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" id="logoutBtn"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row mb-4">
            <div class="col">
                <h2 class="mb-0">Dashboard</h2>
                <p class="text-muted">Welcome back! Here's what's happening with your business today.</p>
            </div>
        </div>
        
        <!-- Stats Cards -->
        <div class="row g-4 mb-4">
            <div class="col-12 col-sm-6 col-lg-3">
                <div class="stat-card">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h3>Total Customers</h3>
                            <p class="stat-value mb-0" id="totalCustomers">0</p>
                            <p class="stat-change mb-0">
                                <i class="bi bi-arrow-up"></i> 12% from last month
                            </p>
                        </div>
                        <div class="bg-primary bg-opacity-10 p-3 rounded">
                            <i class="bi bi-people fs-4 text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-sm-6 col-lg-3">
                <div class="stat-card">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h3>Active Deals</h3>
                            <p class="stat-value mb-0" id="activeDeals">0</p>
                            <p class="stat-change mb-0">
                                <i class="bi bi-arrow-up"></i> 8% from last month
                            </p>
                        </div>
                        <div class="bg-success bg-opacity-10 p-3 rounded">
                            <i class="bi bi-currency-dollar fs-4 text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-sm-6 col-lg-3">
                <div class="stat-card">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h3>Pending Tasks</h3>
                            <p class="stat-value mb-0" id="pendingTasks">0</p>
                            <p class="stat-change negative mb-0">
                                <i class="bi bi-arrow-down"></i> 3% from last month
                            </p>
                        </div>
                        <div class="bg-warning bg-opacity-10 p-3 rounded">
                            <i class="bi bi-check-square fs-4 text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-12 col-sm-6 col-lg-3">
                <div class="stat-card">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h3>Upcoming Events</h3>
                            <p class="stat-value mb-0" id="upcomingEvents">0</p>
                            <p class="stat-change mb-0">
                                <i class="bi bi-arrow-up"></i> 15% from last month
                            </p>
                        </div>
                        <div class="bg-info bg-opacity-10 p-3 rounded">
                            <i class="bi bi-calendar3 fs-4 text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Content Row -->
        <div class="row g-4">
            <!-- Recent Activity -->
            <div class="col-lg-8">
                <div class="card h-100">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Recent Activity</h5>
                        <button class="btn btn-sm btn-outline-secondary">
                            <i class="bi bi-three-dots"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush" id="recentActivityList">
                            <!-- Activity items will be populated here -->
                            <div class="list-group-item border-0 px-0">
                                <div class="d-flex align-items-start">
                                    <div class="bg-primary bg-opacity-10 p-2 rounded me-3">
                                        <i class="bi bi-person-plus text-primary"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">New customer added</h6>
                                        <p class="text-muted small mb-0">John Doe joined as a new customer</p>
                                        <small class="text-muted">2 hours ago</small>
                                    </div>
                                </div>
                            </div>
                            <div class="list-group-item border-0 px-0">
                                <div class="d-flex align-items-start">
                                    <div class="bg-success bg-opacity-10 p-2 rounded me-3">
                                        <i class="bi bi-check-circle text-success"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">Deal closed</h6>
                                        <p class="text-muted small mb-0">Deal #1234 worth $5,000 closed successfully</p>
                                        <small class="text-muted">5 hours ago</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="col-lg-4">
                <div class="card h-100">
                    <div class="card-header">
                        <h5 class="mb-0">Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="location.href='customers.html'">
                                <i class="bi bi-person-plus me-2"></i>Add Customer
                            </button>
                            <button class="btn btn-success" onclick="location.href='deals.html'">
                                <i class="bi bi-currency-dollar me-2"></i>Create Deal
                            </button>
                            <button class="btn btn-warning" onclick="location.href='tasks.html'">
                                <i class="bi bi-check-square me-2"></i>Add Task
                            </button>
                            <button class="btn btn-info" onclick="location.href='calendar.html'">
                                <i class="bi bi-calendar-plus me-2"></i>Schedule Event
                            </button>
                            <button class="btn btn-secondary" onclick="location.href='reports.html'">
                                <i class="bi bi-file-earmark-text me-2"></i>Generate Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap 5 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom Scripts -->
    <script src="js/common-ui.js"></script>
    <script src="js/validation.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/dashboard.js"></script>
    <script>
        // Update user initial in avatar
        document.addEventListener('DOMContentLoaded', function() {
            const username = document.getElementById('username').textContent;
            const initial = username.charAt(0).toUpperCase();
            document.getElementById('userInitial').textContent = initial;
        });
    </script>
</body>
</html>

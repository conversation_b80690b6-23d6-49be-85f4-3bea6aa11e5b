/**
 * Request Logging Middleware
 * 
 * Logs all HTTP requests for monitoring, debugging, and security auditing
 */

const winston = require('winston');
const morgan = require('morgan');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

/**
 * Winston logger configuration
 */
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    winston.format.splat(),
    winston.format.json()
  ),
  defaultMeta: { service: 'crm-api' },
  transports: [
    // Write all logs with level 'error' and below to error.log
    new winston.transports.File({ 
      filename: path.join(logsDir, 'error.log'), 
      level: 'error',
      maxsize: 10485760, // 10MB
      maxFiles: 5
    }),
    // Write all logs with level 'info' and below to combined.log
    new winston.transports.File({ 
      filename: path.join(logsDir, 'combined.log'),
      maxsize: 10485760, // 10MB
      maxFiles: 10
    }),
    // Write access logs
    new winston.transports.File({ 
      filename: path.join(logsDir, 'access.log'),
      level: 'http',
      maxsize: 10485760, // 10MB
      maxFiles: 10
    })
  ]
});

// If we're not in production, log to the console
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

/**
 * Create a custom Morgan token for response time
 */
morgan.token('response-time-ms', (req, res) => {
  if (!req._startAt || !res._startAt) {
    return '0';
  }
  const ms = (res._startAt[0] - req._startAt[0]) * 1000 +
    (res._startAt[1] - req._startAt[1]) / 1000000;
  return ms.toFixed(3);
});

/**
 * Create a custom Morgan token for request ID
 */
morgan.token('request-id', (req) => req.id);

/**
 * Custom Morgan format for structured logging
 */
const morganFormat = (tokens, req, res) => {
  const log = {
    requestId: tokens['request-id'](req, res),
    timestamp: tokens.date(req, res, 'iso'),
    method: tokens.method(req, res),
    url: tokens.url(req, res),
    status: parseInt(tokens.status(req, res)),
    contentLength: tokens.res(req, res, 'content-length'),
    responseTime: parseFloat(tokens['response-time-ms'](req, res)),
    userAgent: tokens['user-agent'](req, res),
    ip: tokens['remote-addr'](req, res),
    referrer: tokens.referrer(req, res),
    user: req.user ? req.user.id : 'anonymous'
  };

  // Add additional context for errors
  if (log.status >= 400) {
    log.error = res.locals.error || 'Unknown error';
  }

  return JSON.stringify(log);
};

/**
 * Morgan middleware for HTTP request logging
 */
const morganMiddleware = morgan(morganFormat, {
  stream: {
    write: (message) => {
      const log = JSON.parse(message.trim());
      logger.http('Request', log);
    }
  },
  skip: (req, res) => {
    // Skip logging for health checks
    return req.url === '/api/health' && res.statusCode === 200;
  }
});

/**
 * Request ID middleware
 * Adds a unique ID to each request for tracking
 */
const requestId = (req, res, next) => {
  req.id = req.headers['x-request-id'] || uuidv4();
  res.setHeader('X-Request-ID', req.id);
  next();
};

/**
 * Request context middleware
 * Adds additional context to requests for logging
 */
const requestContext = (req, res, next) => {
  req.context = {
    startTime: Date.now(),
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.get('user-agent'),
    origin: req.get('origin'),
    referer: req.get('referer')
  };
  next();
};

/**
 * Log request details middleware
 * Logs detailed information about requests
 */
const logRequestDetails = (req, res, next) => {
  const sanitizedBody = { ...req.body };
  // Remove sensitive fields from logs
  const sensitiveFields = ['password', 'token', 'creditCard', 'ssn', 'apiKey'];
  sensitiveFields.forEach(field => {
    if (sanitizedBody[field]) {
      sanitizedBody[field] = '[REDACTED]';
    }
  });

  logger.info('Incoming request', {
    requestId: req.id,
    method: req.method,
    url: req.url,
    headers: {
      'content-type': req.get('content-type'),
      'user-agent': req.get('user-agent'),
      'origin': req.get('origin')
    },
    query: req.query,
    body: sanitizedBody,
    user: req.user ? { id: req.user.id, email: req.user.email } : null
  });

  next();
};

/**
 * Log response details middleware
 * Should be added after routes
 */
const logResponseDetails = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    res.send = originalSend;
    
    const responseTime = Date.now() - req.context.startTime;
    
    logger.info('Outgoing response', {
      requestId: req.id,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      contentLength: res.get('content-length'),
      user: req.user ? { id: req.user.id, email: req.user.email } : null
    });

    // Log slow requests
    if (responseTime > 1000) {
      logger.warn('Slow request detected', {
        requestId: req.id,
        url: req.url,
        responseTime: `${responseTime}ms`
      });
    }

    return res.send(data);
  };

  next();
};

/**
 * Security audit logger
 * Logs security-related events
 */
const securityLogger = {
  logFailedLogin: (email, ip, reason) => {
    logger.warn('Failed login attempt', {
      event: 'FAILED_LOGIN',
      email,
      ip,
      reason,
      timestamp: new Date().toISOString()
    });
  },

  logSuccessfulLogin: (userId, email, ip) => {
    logger.info('Successful login', {
      event: 'SUCCESSFUL_LOGIN',
      userId,
      email,
      ip,
      timestamp: new Date().toISOString()
    });
  },

  logUnauthorizedAccess: (userId, resource, ip) => {
    logger.warn('Unauthorized access attempt', {
      event: 'UNAUTHORIZED_ACCESS',
      userId,
      resource,
      ip,
      timestamp: new Date().toISOString()
    });
  },

  logSuspiciousActivity: (description, details) => {
    logger.error('Suspicious activity detected', {
      event: 'SUSPICIOUS_ACTIVITY',
      description,
      details,
      timestamp: new Date().toISOString()
    });
  },

  logDataAccess: (userId, resource, action, ip) => {
    logger.info('Data access', {
      event: 'DATA_ACCESS',
      userId,
      resource,
      action,
      ip,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Error logger middleware
 * Logs detailed error information
 */
const errorLogger = (err, req, res, next) => {
  logger.error('Request error', {
    requestId: req.id,
    error: {
      message: err.message,
      stack: err.stack,
      code: err.code || 'UNKNOWN'
    },
    request: {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body
    },
    user: req.user ? { id: req.user.id, email: req.user.email } : null
  });

  // Store error for response logging
  res.locals.error = err.message;
  
  next(err);
};

/**
 * Database query logger
 * Logs database queries for debugging
 */
const queryLogger = {
  log: (query, params, duration) => {
    logger.debug('Database query', {
      query,
      params,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString()
    });
  },

  logSlow: (query, params, duration) => {
    logger.warn('Slow database query', {
      query,
      params,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Performance logger
 * Logs performance metrics
 */
const performanceLogger = {
  logMetric: (metric, value, unit = 'ms') => {
    logger.info('Performance metric', {
      metric,
      value,
      unit,
      timestamp: new Date().toISOString()
    });
  }
};

module.exports = {
  logger,
  morganMiddleware,
  requestId,
  requestContext,
  logRequestDetails,
  logResponseDetails,
  securityLogger,
  errorLogger,
  queryLogger,
  performanceLogger
};

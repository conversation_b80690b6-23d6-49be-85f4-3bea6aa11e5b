// Registration functionality
document.addEventListener('DOMContentLoaded', function() {
    const registerForm = document.getElementById('registerForm');
    const messageArea = document.getElementById('messageArea');

    // Attach loading indicator to registration process
    api.onLoadingChange('register', (isLoading) => {
        const submitButton = registerForm?.querySelector('button[type="submit"]');
        if (submitButton) {
            submitButton.disabled = isLoading;
            submitButton.innerHTML = isLoading 
                ? '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Registering...'
                : '<i class="bi bi-check-lg me-2"></i>Register';
        }
    });

    if (registerForm) {
        registerForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // Clear previous messages
            if (messageArea) messageArea.innerHTML = '';

            // Get form data
            const formData = getFormData(registerForm);
            const { fullName, email, username, password, confirmPassword, company } = formData;
            
            // Client-side validation
            const validationErrors = [];

            if (!validateRequired(fullName)) {
                validationErrors.push('Full name is required');
            }

            if (!validateRequired(email)) {
                validationErrors.push('Email is required');
            } else if (!validateEmail(email)) {
                validationErrors.push('Please enter a valid email address');
            }

            if (!validateRequired(username)) {
                validationErrors.push('Username is required');
            }

            if (!validateRequired(password)) {
                validationErrors.push('Password is required');
            } else if (password.length < 6) {
                validationErrors.push('Password must be at least 6 characters long');
            }

            if (!validateRequired(confirmPassword)) {
                validationErrors.push('Please confirm your password');
            } else if (password !== confirmPassword) {
                validationErrors.push('Passwords do not match');
            }

            if (validationErrors.length > 0) {
                if (messageArea) {
                    showError('messageArea', { message: validationErrors.join('<br>') });
                } else {
                    showMessage(validationErrors.join(', '), 'error');
                }
                return;
            }

            try {
                // Attempt to register the user through the API
                const response = await api.register({ fullName, email, username, password, company });

                if (response.success) {
                    if (messageArea) {
                        showSuccess('messageArea', 'Account created successfully! Redirecting to dashboard...');
                    } else {
                        showMessage('Account created successfully! Redirecting to dashboard...', 'success');
                    }

                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1500);
                } else {
                    throw new Error(response.message || 'Registration failed');
                }
            } catch (error) {
                console.error('Registration error:', error);

                const errorMessage = error.status === 409 
                    ? 'This email or username is already in use' 
                    : error.message || 'Registration failed. Please try again.';

                if (messageArea) {
                    showError('messageArea', { message: errorMessage });
                } else {
                    showMessage(errorMessage, 'error');
                }
            }
        });
    }
    
    // Display password strength
    const passwordInput = document.getElementById('password');
    const strengthMeter = document.getElementById('strengthMeter');

    if (passwordInput && strengthMeter) {
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            const strength = calculatePasswordStrength(password);

            strengthMeter.value = strength;
            strengthMeter.textContent = `Strength: ${strength} / 5`;
        });
    }

    // Real-time password confirmation validation
    const confirmPasswordInput = document.getElementById('confirmPassword');
    if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (confirmPassword && password !== confirmPassword) {
                this.classList.add('is-invalid');
                this.classList.remove('is-valid');
            } else {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    }
});

function calculatePasswordStrength(password) {
    let score = 0;
    if (password.length >= 6) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[@#$%^&*()_+\-=[\]{};':"\|,.<>\/?]+/.test(password)) score++;
    return score;
}

// Fallback message display for pages without message area
function showMessage(message, type) {
    // Try to use the global showToast function if available
    if (typeof showToast === 'function') {
        const toastType = type === 'error' ? 'danger' : 'success';
        showToast(message, toastType);
        return;
    }
    
    // Fallback to custom message display
    const existingMessage = document.querySelector('.message');
    if (existingMessage) {
        existingMessage.remove();
    }

    const messageDiv = document.createElement('div');
    messageDiv.className = `alert alert-${type === 'error' ? 'danger' : 'success'} message`;
    messageDiv.innerHTML = `
        <i class="bi bi-${type === 'error' ? 'exclamation-circle' : 'check-circle'}-fill me-2"></i>
        ${message}
    `;

    const form = document.getElementById('registerForm');
    if (form && form.parentNode) {
        form.parentNode.insertBefore(messageDiv, form);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 5000);
    }
}

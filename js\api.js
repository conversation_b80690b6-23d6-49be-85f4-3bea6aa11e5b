// API Configuration
const API_BASE_URL = 'http://localhost:5000/api';

// API Service Class
class ApiService {
    constructor() {
        this.baseURL = API_BASE_URL;
        this.token = localStorage.getItem('authToken');
        this.refreshToken = localStorage.getItem('refreshToken');
        this.isRefreshing = false;
        this.failedQueue = [];
        this.loadingStates = new Map();
        this.loadingCallbacks = new Map();
    }

    // Get CSRF token from cookie
    getCSRFToken() {
        const cookies = document.cookie.split(';');
        for (const cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrf-token') {
                return value;
            }
        }
        return null;
    }

    // Set authorization header and CSRF token
    getHeaders() {
        const headers = {
            'Content-Type': 'application/json'
        };
        
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }
        
        // Add CSRF token for state-changing requests
        const csrfToken = this.getCSRFToken();
        if (csrfToken) {
            headers['X-CSRF-Token'] = csrfToken;
        }
        
        return headers;
    }

    // Process failed queue after token refresh
    processQueue(error, token = null) {
        this.failedQueue.forEach(prom => {
            if (error) {
                prom.reject(error);
            } else {
                prom.resolve(token);
            }
        });
        
        this.failedQueue = [];
    }

    // Refresh authentication token
    async refreshAuthToken() {
        if (this.isRefreshing) {
            return new Promise((resolve, reject) => {
                this.failedQueue.push({ resolve, reject });
            });
        }

        this.isRefreshing = true;

        try {
            const response = await fetch(`${this.baseURL}/auth/refresh`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ refreshToken: this.refreshToken })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                this.token = data.token;
                localStorage.setItem('authToken', data.token);
                if (data.refreshToken) {
                    this.refreshToken = data.refreshToken;
                    localStorage.setItem('refreshToken', data.refreshToken);
                }
                this.processQueue(null, data.token);
                return data.token;
            } else {
                this.processQueue(new Error('Failed to refresh token'), null);
                this.logout();
                return null;
            }
        } catch (error) {
            this.processQueue(error, null);
            this.logout();
            return null;
        } finally {
            this.isRefreshing = false;
        }
    }

    // Handle API response with automatic retry on 401
    async handleResponse(response, retryCount = 0) {
        if (response.status === 401 && retryCount === 0) {
            // Try to refresh token
            const newToken = await this.refreshAuthToken();
            if (newToken) {
                // Retry the original request with new token
                return null; // Signal to retry
            }
        }

        const contentType = response.headers.get('content-type');
        let data;
        
        if (contentType && contentType.includes('application/json')) {
            data = await response.json();
        } else {
            data = await response.text();
        }
        
        if (!response.ok) {
            if (response.status === 401 && retryCount > 0) {
                // Already retried, logout
                this.logout();
            }
            
            const error = new Error(data.error || data.message || 'Something went wrong');
            error.status = response.status;
            error.data = data;
            throw error;
        }
        
        return data;
    }

    // Make API request with retry logic
    async makeRequest(url, options = {}, retryCount = 0) {
        const response = await fetch(url, {
            ...options,
            headers: this.getHeaders()
        });

        const result = await this.handleResponse(response, retryCount);
        
        if (result === null && retryCount === 0) {
            // Retry with new token
            return this.makeRequest(url, options, retryCount + 1);
        }
        
        return result;
    }

    // Loading state management
    setLoading(key, isLoading) {
        this.loadingStates.set(key, isLoading);
        const callbacks = this.loadingCallbacks.get(key) || [];
        callbacks.forEach(callback => callback(isLoading));
    }

    onLoadingChange(key, callback) {
        if (!this.loadingCallbacks.has(key)) {
            this.loadingCallbacks.set(key, []);
        }
        this.loadingCallbacks.get(key).push(callback);
        
        // Return unsubscribe function
        return () => {
            const callbacks = this.loadingCallbacks.get(key) || [];
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        };
    }

    isLoading(key) {
        return this.loadingStates.get(key) || false;
    }

    // Generic request wrapper with loading state
    async request(key, requestFn) {
        this.setLoading(key, true);
        try {
            const result = await requestFn();
            return result;
        } catch (error) {
            throw error;
        } finally {
            this.setLoading(key, false);
        }
    }

    // Auth endpoints
    async login(email, password) {
        return this.request('login', async () => {
            const response = await fetch(`${this.baseURL}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email, password })
            });
            
            const data = await this.handleResponse(response);
            
            if (data.success) {
                this.token = data.token;
                localStorage.setItem('authToken', data.token);
                if (data.refreshToken) {
                    this.refreshToken = data.refreshToken;
                    localStorage.setItem('refreshToken', data.refreshToken);
                }
                localStorage.setItem('user', JSON.stringify(data.user));
            }
            
            return data;
        });
    }

    async register(userData) {
        return this.request('register', async () => {
            const response = await fetch(`${this.baseURL}/auth/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });
            
            const data = await this.handleResponse(response);
            
            if (data.success) {
                this.token = data.token;
                localStorage.setItem('authToken', data.token);
                if (data.refreshToken) {
                    this.refreshToken = data.refreshToken;
                    localStorage.setItem('refreshToken', data.refreshToken);
                }
                localStorage.setItem('user', JSON.stringify(data.user));
            }
            
            return data;
        });
    }

    async logout() {
        try {
            // Try to logout on server
            await fetch(`${this.baseURL}/auth/logout`, {
                method: 'POST',
                headers: this.getHeaders()
            });
        } catch (error) {
            // Ignore errors during logout
        } finally {
            localStorage.removeItem('authToken');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('user');
            this.token = null;
            this.refreshToken = null;
            window.location.href = '/index.html';
        }
    }

    async getMe() {
        return this.request('getMe', async () => {
            return this.makeRequest(`${this.baseURL}/auth/me`);
        });
    }

    // Customer endpoints
    async getCustomers(params = {}) {
        return this.request('customers', async () => {
            const queryString = new URLSearchParams(params).toString();
            return this.makeRequest(`${this.baseURL}/customers?${queryString}`);
        });
    }

    async getCustomer(id) {
        return this.request(`customer-${id}`, async () => {
            return this.makeRequest(`${this.baseURL}/customers/${id}`);
        });
    }

    async createCustomer(customerData) {
        return this.request('createCustomer', async () => {
            return this.makeRequest(`${this.baseURL}/customers`, {
                method: 'POST',
                body: JSON.stringify(customerData)
            });
        });
    }

    async updateCustomer(id, customerData) {
        return this.request(`updateCustomer-${id}`, async () => {
            return this.makeRequest(`${this.baseURL}/customers/${id}`, {
                method: 'PUT',
                body: JSON.stringify(customerData)
            });
        });
    }

    async deleteCustomer(id) {
        return this.request(`deleteCustomer-${id}`, async () => {
            return this.makeRequest(`${this.baseURL}/customers/${id}`, {
                method: 'DELETE'
            });
        });
    }

    // Contact endpoints
    async getContacts(params = {}) {
        return this.request('contacts', async () => {
            const queryString = new URLSearchParams(params).toString();
            return this.makeRequest(`${this.baseURL}/contacts?${queryString}`);
        });
    }

    async getContact(id) {
        return this.request(`contact-${id}`, async () => {
            return this.makeRequest(`${this.baseURL}/contacts/${id}`);
        });
    }

    async createContact(contactData) {
        return this.request('createContact', async () => {
            return this.makeRequest(`${this.baseURL}/contacts`, {
                method: 'POST',
                body: JSON.stringify(contactData)
            });
        });
    }

    async updateContact(id, contactData) {
        return this.request(`updateContact-${id}`, async () => {
            return this.makeRequest(`${this.baseURL}/contacts/${id}`, {
                method: 'PUT',
                body: JSON.stringify(contactData)
            });
        });
    }

    async deleteContact(id) {
        return this.request(`deleteContact-${id}`, async () => {
            return this.makeRequest(`${this.baseURL}/contacts/${id}`, {
                method: 'DELETE'
            });
        });
    }

    // Deal endpoints
    async getDeals(params = {}) {
        return this.request('deals', async () => {
            const queryString = new URLSearchParams(params).toString();
            return this.makeRequest(`${this.baseURL}/deals?${queryString}`);
        });
    }

    async getDeal(id) {
        return this.request(`deal-${id}`, async () => {
            return this.makeRequest(`${this.baseURL}/deals/${id}`);
        });
    }

    async createDeal(dealData) {
        return this.request('createDeal', async () => {
            return this.makeRequest(`${this.baseURL}/deals`, {
                method: 'POST',
                body: JSON.stringify(dealData)
            });
        });
    }

    async updateDeal(id, dealData) {
        return this.request(`updateDeal-${id}`, async () => {
            return this.makeRequest(`${this.baseURL}/deals/${id}`, {
                method: 'PUT',
                body: JSON.stringify(dealData)
            });
        });
    }

    async deleteDeal(id) {
        return this.request(`deleteDeal-${id}`, async () => {
            return this.makeRequest(`${this.baseURL}/deals/${id}`, {
                method: 'DELETE'
            });
        });
    }

    // Task endpoints
    async getTasks(params = {}) {
        return this.request('tasks', async () => {
            const queryString = new URLSearchParams(params).toString();
            return this.makeRequest(`${this.baseURL}/tasks?${queryString}`);
        });
    }

    async getTask(id) {
        return this.request(`task-${id}`, async () => {
            return this.makeRequest(`${this.baseURL}/tasks/${id}`);
        });
    }

    async createTask(taskData) {
        return this.request('createTask', async () => {
            return this.makeRequest(`${this.baseURL}/tasks`, {
                method: 'POST',
                body: JSON.stringify(taskData)
            });
        });
    }

    async updateTask(id, taskData) {
        return this.request(`updateTask-${id}`, async () => {
            return this.makeRequest(`${this.baseURL}/tasks/${id}`, {
                method: 'PUT',
                body: JSON.stringify(taskData)
            });
        });
    }

    async deleteTask(id) {
        return this.request(`deleteTask-${id}`, async () => {
            return this.makeRequest(`${this.baseURL}/tasks/${id}`, {
                method: 'DELETE'
            });
        });
    }

    // Dashboard endpoints
    async getDashboardStats() {
        return this.request('dashboardStats', async () => {
            return this.makeRequest(`${this.baseURL}/dashboard`);
        });
    }

    // Activity endpoints
    async getActivities(params = {}) {
        return this.request('activities', async () => {
            const queryString = new URLSearchParams(params).toString();
            return this.makeRequest(`${this.baseURL}/activities?${queryString}`);
        });
    }

    async createActivity(activityData) {
        return this.request('createActivity', async () => {
            return this.makeRequest(`${this.baseURL}/activities`, {
                method: 'POST',
                body: JSON.stringify(activityData)
            });
        });
    }

    // User endpoints
    async getUsers() {
        return this.request('users', async () => {
            return this.makeRequest(`${this.baseURL}/users`);
        });
    }

    async getUser(id) {
        return this.request(`user-${id}`, async () => {
            return this.makeRequest(`${this.baseURL}/users/${id}`);
        });
    }

    async updateUser(id, userData) {
        return this.request(`updateUser-${id}`, async () => {
            return this.makeRequest(`${this.baseURL}/users/${id}`, {
                method: 'PUT',
                body: JSON.stringify(userData)
            });
        });
    }

    async deleteUser(id) {
        return this.request(`deleteUser-${id}`, async () => {
            return this.makeRequest(`${this.baseURL}/users/${id}`, {
                method: 'DELETE'
            });
        });
    }

    // Calendar endpoints
    async getCalendarEvents(params = {}) {
        return this.request('calendarEvents', async () => {
            const queryString = new URLSearchParams(params).toString();
            return this.makeRequest(`${this.baseURL}/calendar?${queryString}`);
        });
    }

    async createCalendarEvent(eventData) {
        return this.request('createCalendarEvent', async () => {
            return this.makeRequest(`${this.baseURL}/calendar`, {
                method: 'POST',
                body: JSON.stringify(eventData)
            });
        });
    }

    async updateCalendarEvent(id, eventData) {
        return this.request(`updateCalendarEvent-${id}`, async () => {
            return this.makeRequest(`${this.baseURL}/calendar/${id}`, {
                method: 'PUT',
                body: JSON.stringify(eventData)
            });
        });
    }

    async deleteCalendarEvent(id) {
        return this.request(`deleteCalendarEvent-${id}`, async () => {
            return this.makeRequest(`${this.baseURL}/calendar/${id}`, {
                method: 'DELETE'
            });
        });
    }
}

// Create global instance
const api = new ApiService();

// Auth helper functions
function isAuthenticated() {
    // Check both API token and local auth
    return !!localStorage.getItem('authToken') || !!localStorage.getItem('loggedInUser');
}

function getCurrentUser() {
    // Try API user first
    const userStr = localStorage.getItem('user');
    if (userStr) {
        return JSON.parse(userStr);
    }
    
    // Fallback to local auth user
    const currentUserStr = localStorage.getItem('currentUser');
    return currentUserStr ? JSON.parse(currentUserStr) : null;
}

function requireAuth() {
    if (!isAuthenticated()) {
        window.location.href = '/index.html';
        return false;
    }
    return true;
}

// UI Helper functions
function showLoading(elementId, message = 'Loading...') {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="text-center p-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">${message}</span>
                </div>
                <p class="mt-2">${message}</p>
            </div>
        `;
    }
}

function showError(elementId, error) {
    const element = document.getElementById(elementId);
    if (element) {
        const message = error.message || 'An error occurred';
        element.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                ${message}
            </div>
        `;
    }
}

function showSuccess(elementId, message) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle-fill me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
    }
}

// Toast notifications
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(container);
    }

    const toastId = `toast-${Date.now()}`;
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;

    document.getElementById('toast-container').insertAdjacentHTML('beforeend', toastHtml);
    
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement);
    toast.show();
    
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

// Form validation helpers
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

function validatePhone(phone) {
    const re = /^[\d\s\-\+\(\)]+$/;
    return re.test(phone);
}

function validateRequired(value) {
    return value && value.trim().length > 0;
}

// Form helper to get form data as object
function getFormData(formElement) {
    const formData = new FormData(formElement);
    const data = {};
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    return data;
}

// Initialize auth check on protected pages
document.addEventListener('DOMContentLoaded', function() {
    const publicPages = ['index.html', 'register.html'];
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    
    if (!publicPages.includes(currentPage) && !isAuthenticated()) {
        window.location.href = '/index.html';
    }
    
    // Setup global error handler for unhandled promise rejections
    window.addEventListener('unhandledrejection', event => {
        console.error('Unhandled promise rejection:', event.reason);
        showToast('An unexpected error occurred. Please try again.', 'danger');
    });
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        api,
        isAuthenticated,
        getCurrentUser,
        requireAuth,
        showLoading,
        showError,
        showSuccess,
        showToast,
        validateEmail,
        validatePhone,
        validateRequired,
        getFormData
    };
}

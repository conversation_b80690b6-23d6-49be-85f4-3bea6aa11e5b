const { Calendar, Customer, Contact, Deal, Task, User } = require('../models');
const { Op } = require('sequelize');

// @desc    Get all calendar events
// @route   GET /api/calendar
// @access  Private
exports.getEvents = async (req, res, next) => {
  try {
    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 50;
    const offset = (page - 1) * limit;

    // Filtering
    const where = {};
    
    // Date range filter
    if (req.query.startDate && req.query.endDate) {
      where[Op.or] = [
        {
          startDate: {
            [Op.between]: [new Date(req.query.startDate), new Date(req.query.endDate)]
          }
        },
        {
          endDate: {
            [Op.between]: [new Date(req.query.startDate), new Date(req.query.endDate)]
          }
        }
      ];
    } else if (req.query.startDate) {
      where.startDate = { [Op.gte]: new Date(req.query.startDate) };
    } else if (req.query.endDate) {
      where.endDate = { [Op.lte]: new Date(req.query.endDate) };
    }
    
    if (req.query.type) {
      where.type = req.query.type;
    }
    
    if (req.query.status) {
      where.status = req.query.status;
    }
    
    if (req.query.assignedToId) {
      where.assignedToId = req.query.assignedToId;
    }
    
    if (req.query.customerId) {
      where.customerId = req.query.customerId;
    }
    
    if (req.query.search) {
      where[Op.or] = [
        { title: { [Op.iLike]: `%${req.query.search}%` } },
        { description: { [Op.iLike]: `%${req.query.search}%` } },
        { location: { [Op.iLike]: `%${req.query.search}%` } }
      ];
    }

    // Sorting
    const order = [['startDate', 'ASC']];

    const { count, rows } = await Calendar.findAndCountAll({
      where,
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'companyName']
        },
        {
          model: Contact,
          as: 'contact',
          attributes: ['id', 'firstName', 'lastName']
        },
        {
          model: Deal,
          as: 'deal',
          attributes: ['id', 'title']
        },
        {
          model: Task,
          as: 'task',
          attributes: ['id', 'title']
        },
        {
          model: User,
          as: 'assignedTo',
          attributes: ['id', 'firstName', 'lastName']
        }
      ],
      order,
      limit,
      offset,
      distinct: true
    });

    res.status(200).json({
      success: true,
      count,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(count / limit)
      },
      data: rows
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single calendar event
// @route   GET /api/calendar/:id
// @access  Private
exports.getEvent = async (req, res, next) => {
  try {
    const event = await Calendar.findByPk(req.params.id, {
      include: [
        {
          model: Customer,
          as: 'customer'
        },
        {
          model: Contact,
          as: 'contact'
        },
        {
          model: Deal,
          as: 'deal'
        },
        {
          model: Task,
          as: 'task'
        },
        {
          model: User,
          as: 'assignedTo',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'firstName', 'lastName']
        }
      ]
    });

    if (!event) {
      return res.status(404).json({
        success: false,
        error: 'Calendar event not found'
      });
    }

    res.status(200).json({
      success: true,
      data: event
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create new calendar event
// @route   POST /api/calendar
// @access  Private
exports.createEvent = async (req, res, next) => {
  try {
    req.body.createdById = req.user.id;
    
    if (!req.body.assignedToId) {
      req.body.assignedToId = req.user.id;
    }

    const event = await Calendar.create(req.body);

    // Load with associations
    const eventWithAssociations = await Calendar.findByPk(event.id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'companyName']
        },
        {
          model: User,
          as: 'assignedTo',
          attributes: ['id', 'firstName', 'lastName']
        }
      ]
    });

    res.status(201).json({
      success: true,
      data: eventWithAssociations
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update calendar event
// @route   PUT /api/calendar/:id
// @access  Private
exports.updateEvent = async (req, res, next) => {
  try {
    let event = await Calendar.findByPk(req.params.id);

    if (!event) {
      return res.status(404).json({
        success: false,
        error: 'Calendar event not found'
      });
    }

    event = await event.update(req.body);

    res.status(200).json({
      success: true,
      data: event
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete calendar event
// @route   DELETE /api/calendar/:id
// @access  Private
exports.deleteEvent = async (req, res, next) => {
  try {
    const event = await Calendar.findByPk(req.params.id);

    if (!event) {
      return res.status(404).json({
        success: false,
        error: 'Calendar event not found'
      });
    }

    // Only allow deletion by creator or admin
    if (event.createdById !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: 'Not authorized to delete this event'
      });
    }

    await event.destroy();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get upcoming events
// @route   GET /api/calendar/upcoming
// @access  Private
exports.getUpcomingEvents = async (req, res, next) => {
  try {
    const limit = parseInt(req.query.limit, 10) || 10;
    const now = new Date();
    
    const events = await Calendar.findAll({
      where: {
        assignedToId: req.user.id,
        startDate: { [Op.gte]: now },
        status: 'scheduled'
      },
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'companyName']
        },
        {
          model: Contact,
          as: 'contact',
          attributes: ['id', 'firstName', 'lastName']
        }
      ],
      order: [['startDate', 'ASC']],
      limit
    });

    res.status(200).json({
      success: true,
      count: events.length,
      data: events
    });
  } catch (error) {
    next(error);
  }
};

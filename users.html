<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM System - User Management</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/modern-ui.css">
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1>CRM System</h1>
            </div>
            <div class="user-info">
                <span id="username">User</span>
                <span id="userRole" class="user-role">Role</span>
                <button id="logoutBtn">Logout</button>
            </div>
        </header>
        
        <nav>
            <ul>
                <li><a href="dashboard.html">Dashboard</a></li>
                <li><a href="customers.html">Customers</a></li>
                <li><a href="contacts.html">Contacts</a></li>
                <li><a href="deals.html">Deals</a></li>
                <li><a href="tasks.html">Tasks</a></li>
                <li><a href="calendar.html">Calendar</a></li>
                <li><a href="reports.html">Reports</a></li>
                <li><a href="users.html" class="active admin-only">Users</a></li>
            </ul>
        </nav>
        
        <main>
            <h2>User Management</h2>
            
            <div class="actions-bar">
                <button id="addUserBtn" class="btn-primary admin-only">Add New User</button>
                <div class="search-box">
                    <input type="text" id="userSearch" placeholder="Search users...">
                    <button id="searchBtn">Search</button>
                </div>
            </div>
            
            <div class="user-form-container" id="userFormContainer" style="display: none;">
                <h3 id="formTitle">Add New User</h3>
                <form id="userForm">
                    <input type="hidden" id="userId">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="userFullName">Full Name</label>
                            <input type="text" id="userFullName" name="userFullName" required>
                        </div>
                        <div class="form-group">
                            <label for="userEmail">Email</label>
                            <input type="email" id="userEmail" name="userEmail" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="userUsername">Username</label>
                            <input type="text" id="userUsername" name="userUsername" required>
                        </div>
                        <div class="form-group">
                            <label for="userRole">Role</label>
                            <select id="userRoleSelect" name="userRole" required>
                                <option value="user">User</option>
                                <option value="manager">Manager</option>
                                <option value="admin">Administrator</option>
                                <option value="viewer">Viewer</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="userPassword">Password</label>
                            <input type="password" id="userPassword" name="userPassword" required>
                        </div>
                        <div class="form-group">
                            <label for="userConfirmPassword">Confirm Password</label>
                            <input type="password" id="userConfirmPassword" name="userConfirmPassword" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="userDepartment">Department</label>
                        <input type="text" id="userDepartment" name="userDepartment">
                    </div>
                    <div class="form-group">
                        <label for="userNotes">Notes</label>
                        <textarea id="userNotes" name="userNotes" rows="3"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">Save User</button>
                        <button type="button" id="cancelUserBtn" class="btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
            
            <div class="table-container">
                <table id="usersTable">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Department</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="usersTableBody">
                        <!-- User rows will be populated here -->
                    </tbody>
                </table>
            </div>
            
            <div class="role-permissions">
                <h3>Role Permissions</h3>
                <div class="permissions-grid">
                    <div class="permission-card">
                        <h4>Administrator</h4>
                        <ul>
                            <li>Full system access</li>
                            <li>User management</li>
                            <li>System settings</li>
                            <li>All reports</li>
                        </ul>
                    </div>
                    <div class="permission-card">
                        <h4>Manager</h4>
                        <ul>
                            <li>Read/Write access</li>
                            <li>Delete records</li>
                            <li>View reports</li>
                            <li>User management</li>
                        </ul>
                    </div>
                    <div class="permission-card">
                        <h4>User</h4>
                        <ul>
                            <li>Read/Write access</li>
                            <li>Create records</li>
                            <li>Edit own data</li>
                            <li>Basic reports</li>
                        </ul>
                    </div>
                    <div class="permission-card">
                        <h4>Viewer</h4>
                        <ul>
                            <li>Read-only access</li>
                            <li>View records</li>
                            <li>No editing</li>
                            <li>Limited reports</li>
                        </ul>
                    </div>
                </div>
            </div>
        </main>
    </div>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/users.js"></script>
</body>
</html> 
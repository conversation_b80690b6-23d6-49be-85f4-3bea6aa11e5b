<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM System - Calendar</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/modern-ui.css">
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1>CRM System</h1>
            </div>
            <div class="user-info">
                <span id="username">User</span>
                <span id="userRole" class="user-role">Role</span>
                <button id="logoutBtn">Logout</button>
            </div>
        </header>
        
        <nav>
            <ul>
                <li><a href="dashboard.html">Dashboard</a></li>
                <li><a href="customers.html">Customers</a></li>
                <li><a href="contacts.html">Contacts</a></li>
                <li><a href="deals.html">Deals</a></li>
                <li><a href="tasks.html">Tasks</a></li>
                <li><a href="calendar.html" class="active">Calendar</a></li>
                <li><a href="reports.html">Reports</a></li>
                <li><a href="users.html" class="admin-only">Users</a></li>
                <li><a href="email.html">Email</a></li>
            </ul>
        </nav>
        
        <main>
            <h2>Calendar</h2>
            
            <div class="calendar-header">
                <button id="prevMonthBtn">< Prev</button>
                <h3 id="currentMonthYear">Month Year</h3>
                <button id="nextMonthBtn">Next ></button>
            </div>
            
            <div class="calendar-container">
                <div class="calendar">
                    <div class="weekdays">
                        <div>Sun</div>
                        <div>Mon</div>
                        <div>Tue</div>
                        <div>Wed</div>
                        <div>Thu</div>
                        <div>Fri</div>
                        <div>Sat</div>
                    </div>
                    <div class="days" id="calendarDays">
                        <!-- Calendar days will be populated here -->
                    </div>
                </div>
                
                <div class="event-sidebar">
                    <h3>Events</h3>
                    <button id="addEventBtn" class="btn-primary">Add Event</button>
                    
                    <div class="event-form-container" id="eventFormContainer" style="display: none;">
                        <h4 id="eventFormTitle">Add New Event</h4>
                        <form id="eventForm">
                            <input type="hidden" id="eventId">
                            <div class="form-group">
                                <label for="eventTitle">Event Title</label>
                                <input type="text" id="eventTitle" name="eventTitle" required>
                            </div>
                            <div class="form-group">
                                <label for="eventDate">Date</label>
                                <input type="date" id="eventDate" name="eventDate" required>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="eventStartTime">Start Time</label>
                                    <input type="time" id="eventStartTime" name="eventStartTime" required>
                                </div>
                                <div class="form-group">
                                    <label for="eventEndTime">End Time</label>
                                    <input type="time" id="eventEndTime" name="eventEndTime" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="eventDescription">Description</label>
                                <textarea id="eventDescription" name="eventDescription" rows="3"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="eventRelatedTo">Related To</label>
                                <select id="eventRelatedTo" name="eventRelatedTo">
                                    <option value="">None</option>
                                    <option value="customer">Customer</option>
                                    <option value="deal">Deal</option>
                                </select>
                            </div>
                            <div class="form-group" id="eventCustomerGroup" style="display: none;">
                                <label for="eventCustomer">Customer</label>
                                <select id="eventCustomer" name="eventCustomer">
                                    <option value="">Select Customer</option>
                                    <!-- Customer options will be populated here -->
                                </select>
                            </div>
                            <div class="form-group" id="eventDealGroup" style="display: none;">
                                <label for="eventDeal">Deal</label>
                                <select id="eventDeal" name="eventDeal">
                                    <option value="">Select Deal</option>
                                    <!-- Deal options will be populated here -->
                                </select>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn-primary">Save Event</button>
                                <button type="button" id="cancelEventBtn" class="btn-secondary">Cancel</button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="events-list">
                        <h4>Upcoming Events</h4>
                        <ul id="upcomingEventsList">
                            <!-- Upcoming events will be populated here -->
                        </ul>
                    </div>
                </div>
            </div>
        </main>
    </div>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/calendar.js"></script>
</body>
</html>
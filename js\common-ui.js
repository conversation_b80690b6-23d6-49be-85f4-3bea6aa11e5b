// Common UI Components for CRM System

// Navigation HTML template
function getNavigationHTML(activePage = '') {
    return `
    <!-- Modern Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-custom sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="bi bi-graph-up me-2"></i>CRM System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMain">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarMain">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link ${activePage === 'dashboard' ? 'active' : ''}" href="dashboard.html">
                            <i class="bi bi-speedometer2 me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${activePage === 'customers' ? 'active' : ''}" href="customers.html">
                            <i class="bi bi-people me-1"></i>Customers
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${activePage === 'contacts' ? 'active' : ''}" href="contacts.html">
                            <i class="bi bi-person-lines-fill me-1"></i>Contacts
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${activePage === 'deals' ? 'active' : ''}" href="deals.html">
                            <i class="bi bi-currency-dollar me-1"></i>Deals
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${activePage === 'tasks' ? 'active' : ''}" href="tasks.html">
                            <i class="bi bi-check-square me-1"></i>Tasks
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${activePage === 'calendar' ? 'active' : ''}" href="calendar.html">
                            <i class="bi bi-calendar3 me-1"></i>Calendar
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link ${activePage === 'reports' ? 'active' : ''}" href="reports.html">
                            <i class="bi bi-bar-chart me-1"></i>Reports
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle ${activePage === 'email' || activePage === 'users' ? 'active' : ''}" href="#" data-bs-toggle="dropdown">
                            <i class="bi bi-three-dots me-1"></i>More
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item ${activePage === 'email' ? 'active' : ''}" href="email.html">
                                <i class="bi bi-envelope me-2"></i>Email
                            </a></li>
                            <li><a class="dropdown-item ${activePage === 'users' ? 'active' : ''}" href="users.html">
                                <i class="bi bi-people-fill me-2"></i>Users
                            </a></li>
                        </ul>
                    </li>
                </ul>
                
                <!-- User Profile Dropdown -->
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle d-flex align-items-center" type="button" data-bs-toggle="dropdown">
                        <div class="user-avatar me-2">
                            <span id="userInitial">U</span>
                        </div>
                        <div class="text-start">
                            <div class="fw-semibold" id="username">User</div>
                            <div class="small text-muted" id="userRole">Role</div>
                        </div>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="#"><i class="bi bi-person me-2"></i>Profile</a></li>
                        <li><a class="dropdown-item" href="#"><i class="bi bi-gear me-2"></i>Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" id="logoutBtn"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>`;
}

// Initialize common UI components
function initializeCommonUI(activePage = '') {
    // Insert navigation if container exists
    const navContainer = document.getElementById('navigationContainer');
    if (navContainer) {
        navContainer.innerHTML = getNavigationHTML(activePage);
    }
    
    // Update user info
    updateUserInfo();
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

// Update user information in navigation
function updateUserInfo() {
    const userInfo = JSON.parse(localStorage.getItem('user') || '{}');
    if (userInfo.name) {
        const usernameEl = document.getElementById('username');
        const userRoleEl = document.getElementById('userRole');
        const userInitialEl = document.getElementById('userInitial');
        
        if (usernameEl) usernameEl.textContent = userInfo.name;
        if (userRoleEl) userRoleEl.textContent = userInfo.role || 'User';
        if (userInitialEl) userInitialEl.textContent = userInfo.name.charAt(0).toUpperCase();
    }
}

// Show toast notification
function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        const container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(container);
    }
    
    const toastId = 'toast-' + Date.now();
    const toastHTML = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    document.getElementById('toastContainer').insertAdjacentHTML('beforeend', toastHTML);
    const toastEl = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastEl);
    toast.show();
    
    // Remove toast element after it's hidden
    toastEl.addEventListener('hidden.bs.toast', () => {
        toastEl.remove();
    });
}

// Loading overlay
function showLoading(target = document.body, message = 'Loading...') {
    // Remove any existing loading overlay
    hideLoading(target);
    
    const loadingHTML = `
        <div class="loading-overlay">
            <div class="loading-content">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">${message}</span>
                </div>
                <p class="mt-2 mb-0">${message}</p>
            </div>
        </div>
    `;
    
    if (typeof target === 'string') {
        target = document.getElementById(target) || document.querySelector(target);
    }
    
    if (target) {
        target.insertAdjacentHTML('beforeend', loadingHTML);
        target.classList.add('position-relative');
    }
}

function hideLoading(target = document.body) {
    if (typeof target === 'string') {
        target = document.getElementById(target) || document.querySelector(target);
    }
    
    if (target) {
        const overlay = target.querySelector('.loading-overlay');
        if (overlay) {
            overlay.remove();
            target.classList.remove('position-relative');
        }
    }
}

// Show inline loading spinner
function showInlineLoading(elementId, message = 'Loading...') {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = `
            <div class="text-center p-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">${message}</span>
                </div>
                <p class="mt-2">${message}</p>
            </div>
        `;
    }
}

// Show error message
function showError(elementId, error, dismissible = true) {
    const element = document.getElementById(elementId);
    if (element) {
        const message = error.message || error || 'An error occurred';
        const dismissButton = dismissible ? 
            '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' : '';
        
        element.innerHTML = `
            <div class="alert alert-danger ${dismissible ? 'alert-dismissible fade show' : ''}" role="alert">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                ${message}
                ${dismissButton}
            </div>
        `;
    }
}

// Show success message
function showSuccess(elementId, message, dismissible = true) {
    const element = document.getElementById(elementId);
    if (element) {
        const dismissButton = dismissible ? 
            '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' : '';
        
        element.innerHTML = `
            <div class="alert alert-success ${dismissible ? 'alert-dismissible fade show' : ''}" role="alert">
                <i class="bi bi-check-circle-fill me-2"></i>
                ${message}
                ${dismissButton}
            </div>
        `;
    }
}

// Setup API loading listeners for a component
function setupLoadingListeners(componentKey, loadingElementId, loadingMessage = 'Loading...') {
    if (typeof api !== 'undefined' && api.onLoadingChange) {
        return api.onLoadingChange(componentKey, (isLoading) => {
            if (isLoading) {
                showInlineLoading(loadingElementId, loadingMessage);
            }
        });
    }
    return null;
}

// Format date
function formatDate(dateString) {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

// Status badge helper
function getStatusBadge(status) {
    const statusClasses = {
        'active': 'badge-status-active',
        'pending': 'badge-status-pending',
        'inactive': 'badge-status-inactive',
        'completed': 'badge-status-active',
        'in-progress': 'badge-status-pending',
        'cancelled': 'badge-status-inactive'
    };
    
    const className = statusClasses[status.toLowerCase()] || 'badge-secondary';
    return `<span class="badge ${className}">${status}</span>`;
}

// Export functions for use in other scripts
window.commonUI = {
    initializeCommonUI,
    showToast,
    showLoading,
    hideLoading,
    showInlineLoading,
    showError,
    showSuccess,
    setupLoadingListeners,
    formatDate,
    formatCurrency,
    getStatusBadge
};

// Authentication and Authorization System
class AuthSystem {
    constructor() {
        this.currentUser = null;
        this.userRoles = {
            admin: {
                name: 'Administrator',
                permissions: ['all'],
                color: '#e74c3c'
            },
            manager: {
                name: 'Manager',
                permissions: ['read', 'write', 'delete', 'reports', 'users'],
                color: '#f39c12'
            },
            user: {
                name: 'User',
                permissions: ['read', 'write'],
                color: '#3498db'
            },
            viewer: {
                name: 'Viewer',
                permissions: ['read'],
                color: '#95a5a6'
            }
        };
    }

    // Initialize authentication
    init() {
        const loggedInUser = localStorage.getItem('loggedInUser');
        const currentUserData = localStorage.getItem('currentUser');
        
        if (loggedInUser && currentUserData) {
            this.currentUser = JSON.parse(currentUserData);
            this.updateUI();
        } else {
            // Only redirect if we're not already on the login page
            if (!window.location.pathname.includes('index.html') && !window.location.pathname.includes('login')) {
                this.redirectToLogin();
            }
        }
    }

    // Check if user has permission
    hasPermission(permission) {
        if (!this.currentUser) return false;
        
        const userRole = this.userRoles[this.currentUser.role];
        if (!userRole) return false;
        
        return userRole.permissions.includes('all') || userRole.permissions.includes(permission);
    }

    // Update UI based on user role
    updateUI() {
        const usernameElement = document.getElementById('username');
        const userRoleElement = document.getElementById('userRole');
        
        if (usernameElement) {
            usernameElement.textContent = this.currentUser.fullName || this.currentUser.username;
        }
        
        if (userRoleElement) {
            const role = this.userRoles[this.currentUser.role];
            userRoleElement.textContent = role.name;
            userRoleElement.style.color = role.color;
        }
        
        // Hide/show elements based on permissions
        this.updatePermissionBasedUI();
    }

    // Update UI elements based on permissions
    updatePermissionBasedUI() {
        // Hide delete buttons for viewers
        if (!this.hasPermission('delete')) {
            const deleteButtons = document.querySelectorAll('[onclick*="delete"]');
            deleteButtons.forEach(btn => btn.style.display = 'none');
        }
        
        // Hide admin features for non-admins
        if (!this.hasPermission('users')) {
            const adminElements = document.querySelectorAll('.admin-only');
            adminElements.forEach(el => el.style.display = 'none');
        }
        
        // Hide reports for users without report permission
        if (!this.hasPermission('reports')) {
            const reportsLink = document.querySelector('a[href="reports.html"]');
            if (reportsLink) {
                reportsLink.style.display = 'none';
            }
        }
    }

    // Login user
    login(emailOrUsername, password) {
        const users = JSON.parse(localStorage.getItem('users') || '[]');
        const user = users.find(u => 
            (u.email === emailOrUsername || u.username === emailOrUsername) && 
            atob(u.password) === password
        );
        
        if (user) {
            this.currentUser = user;
            localStorage.setItem('loggedInUser', user.username);
            localStorage.setItem('currentUser', JSON.stringify(user));
            return true;
        }
        return false;
    }

    // Logout user
    logout() {
        this.currentUser = null;
        localStorage.removeItem('loggedInUser');
        localStorage.removeItem('currentUser');
        this.redirectToLogin();
    }

    // Redirect to login
    redirectToLogin() {
        if (window.location.pathname !== '/index.html' && !window.location.pathname.includes('index.html')) {
            window.location.href = 'index.html';
        }
    }

    // Create user
    createUser(userData) {
        const users = JSON.parse(localStorage.getItem('users') || '[]');
        
        // Check if username already exists
        if (users.some(u => u.username === userData.username)) {
            return { success: false, message: 'Username already exists' };
        }
        
        const newUser = {
            id: Date.now(),
            ...userData,
            password: btoa(userData.password),
            createdAt: new Date().toISOString(),
            role: userData.role || 'user'
        };
        
        users.push(newUser);
        localStorage.setItem('users', JSON.stringify(users));
        
        return { success: true, user: newUser };
    }

    // Update user
    updateUser(userId, updates) {
        const users = JSON.parse(localStorage.getItem('users') || '[]');
        const index = users.findIndex(u => u.id == userId);
        
        if (index !== -1) {
            users[index] = { ...users[index], ...updates };
            localStorage.setItem('users', JSON.stringify(users));
            return { success: true };
        }
        
        return { success: false, message: 'User not found' };
    }

    // Delete user
    deleteUser(userId) {
        const users = JSON.parse(localStorage.getItem('users') || '[]');
        const filteredUsers = users.filter(u => u.id != userId);
        
        if (filteredUsers.length !== users.length) {
            localStorage.setItem('users', JSON.stringify(filteredUsers));
            return { success: true };
        }
        
        return { success: false, message: 'User not found' };
    }

    // Get all users (admin only)
    getAllUsers() {
        if (!this.hasPermission('users')) {
            return [];
        }
        
        return JSON.parse(localStorage.getItem('users') || '[]');
    }

    // Get user role info
    getUserRoleInfo(role) {
        return this.userRoles[role] || this.userRoles.user;
    }

    // Get all roles
    getAllRoles() {
        return Object.keys(this.userRoles);
    }
}

// Global auth instance
const auth = new AuthSystem();

// Initialize auth on page load
document.addEventListener('DOMContentLoaded', function() {
    auth.init();
}); 
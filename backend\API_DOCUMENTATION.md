# CRM System API Documentation

## Base URL
`http://localhost:5000/api`

## Authentication
All endpoints except `/auth/register` and `/auth/login` require authentication via Bear<PERSON> token in the Authorization header:
```
Authorization: Bearer <token>
```

## Common Response Format
```json
{
  "success": true|false,
  "data": {} | [],
  "error": "Error message (on failure)",
  "count": number (for list endpoints),
  "pagination": {
    "page": 1,
    "limit": 25,
    "totalPages": 10
  }
}
```

## Endpoints

### Authentication

#### POST /auth/register
Create a new user account.
- **Body**: 
  - `firstName` (required)
  - `lastName` (required)
  - `email` (required)
  - `password` (required, min 6 chars with number)
  - `role` (optional: 'admin', 'manager', 'agent')

#### POST /auth/login
Login to get authentication token.
- **Body**: 
  - `email` (required)
  - `password` (required)

#### GET /auth/me
Get current user profile.
- **Auth**: Required

#### PUT /auth/updatedetails
Update user details.
- **Auth**: Required
- **Body**: 
  - `firstName` (optional)
  - `lastName` (optional)
  - `email` (optional)

#### PUT /auth/updatepassword
Update user password.
- **Auth**: Required
- **Body**: 
  - `currentPassword` (required)
  - `newPassword` (required, min 6 chars with number)

### Customers

#### GET /customers
Get all customers with pagination, filtering, and search.
- **Auth**: Required
- **Query Params**:
  - `page` (default: 1)
  - `limit` (default: 25, max: 100)
  - `status` (filter by status)
  - `rating` (filter by rating)
  - `search` (search in company name, email, contacts)
  - `sortBy` (format: field:order, e.g., createdAt:DESC)

#### GET /customers/:id
Get single customer with full details.
- **Auth**: Required
- **Params**: `id` (UUID)

#### POST /customers
Create new customer.
- **Auth**: Required
- **Body**:
  - `companyName` (required)
  - `email` (required)
  - `phone` (optional)
  - `website` (optional)
  - `industry` (optional)
  - `status` ('lead', 'active', 'inactive')
  - `rating` ('hot', 'warm', 'cold')
  - `annualRevenue` (optional)
  - `employees` (optional)
  - `assignedToId` (optional, defaults to current user)

#### PUT /customers/:id
Update customer.
- **Auth**: Required
- **Params**: `id` (UUID)
- **Body**: Same as create (all optional)

#### DELETE /customers/:id
Delete customer.
- **Auth**: Required (Admin/Manager only)
- **Params**: `id` (UUID)

#### GET /customers/stats
Get customer statistics.
- **Auth**: Required

### Contacts

#### GET /contacts
Get all contacts with pagination and filtering.
- **Auth**: Required
- **Query Params**:
  - `page`, `limit`
  - `customerId` (filter by customer)
  - `isPrimary` (filter primary contacts)
  - `search` (search in names, email, job title)
  - `sortBy`

#### GET /contacts/:id
Get single contact.
- **Auth**: Required
- **Params**: `id` (UUID)

#### POST /contacts
Create new contact.
- **Auth**: Required
- **Body**:
  - `firstName` (required)
  - `lastName` (required)
  - `email` (required)
  - `customerId` (required)
  - `phone`, `mobile` (optional)
  - `jobTitle`, `department` (optional)
  - `isPrimary` (optional, boolean)
  - `birthday` (optional, date)
  - Address fields (optional)

#### PUT /contacts/:id
Update contact.
- **Auth**: Required
- **Params**: `id` (UUID)
- **Body**: Same as create (all optional)

#### DELETE /contacts/:id
Delete contact.
- **Auth**: Required (Admin/Manager only)
- **Params**: `id` (UUID)

### Deals

#### GET /deals
Get all deals with pagination and filtering.
- **Auth**: Required
- **Query Params**:
  - `page`, `limit`
  - `stage` (filter by stage)
  - `status` (filter by status)
  - `customerId`, `assignedToId`
  - `minValue`, `maxValue` (filter by value range)
  - `search` (search in title, description)
  - `sortBy`

#### GET /deals/:id
Get single deal with full details.
- **Auth**: Required
- **Params**: `id` (UUID)

#### POST /deals
Create new deal.
- **Auth**: Required
- **Body**:
  - `title` (required)
  - `value` (required, positive number)
  - `stage` (required: 'prospecting', 'qualification', 'proposal', 'negotiation', 'closed-won', 'closed-lost')
  - `expectedCloseDate` (required)
  - `customerId` (required)
  - `description` (optional)
  - `status` ('active', 'inactive')
  - `probability` (0-100)
  - `contactId`, `assignedToId` (optional)

#### PUT /deals/:id
Update deal.
- **Auth**: Required
- **Params**: `id` (UUID)
- **Body**: Same as create (all optional)

#### DELETE /deals/:id
Delete deal.
- **Auth**: Required (Admin/Manager only)
- **Params**: `id` (UUID)

#### GET /deals/stats
Get deal statistics by stage.
- **Auth**: Required

### Tasks

#### GET /tasks
Get all tasks with pagination and filtering.
- **Auth**: Required
- **Query Params**:
  - `page`, `limit`
  - `status`, `priority`, `type`
  - `customerId`, `assignedToId`, `dealId`
  - `dueDateFrom`, `dueDateTo` (date range)
  - `overdue` (boolean, filter overdue tasks)
  - `search` (search in title, description)
  - `sortBy`

#### GET /tasks/:id
Get single task.
- **Auth**: Required
- **Params**: `id` (UUID)

#### POST /tasks
Create new task.
- **Auth**: Required
- **Body**:
  - `title` (required)
  - `type` (required: 'call', 'email', 'meeting', 'followup', 'deadline')
  - `dueDate` (required)
  - `description` (optional)
  - `priority` ('low', 'medium', 'high')
  - `status` ('pending', 'in-progress', 'completed', 'cancelled')
  - `customerId`, `contactId`, `dealId`, `assignedToId` (optional)

#### PUT /tasks/:id
Update task.
- **Auth**: Required
- **Params**: `id` (UUID)
- **Body**: Same as create (all optional)

#### PUT /tasks/:id/complete
Mark task as complete.
- **Auth**: Required
- **Params**: `id` (UUID)

#### DELETE /tasks/:id
Delete task.
- **Auth**: Required (Admin/Manager only)
- **Params**: `id` (UUID)

### Calendar

#### GET /calendar
Get all calendar events with pagination and filtering.
- **Auth**: Required
- **Query Params**:
  - `page`, `limit`
  - `startDate`, `endDate` (date range)
  - `type`, `status`
  - `assignedToId`, `customerId`
  - `search` (search in title, description, location)

#### GET /calendar/upcoming
Get upcoming events for current user.
- **Auth**: Required
- **Query Params**:
  - `limit` (default: 10)

#### GET /calendar/:id
Get single calendar event.
- **Auth**: Required
- **Params**: `id` (UUID)

#### POST /calendar
Create new calendar event.
- **Auth**: Required
- **Body**:
  - `title` (required)
  - `startDate` (required)
  - `endDate` (required, must be after startDate)
  - `description`, `location` (optional)
  - `type` ('meeting', 'call', 'task', 'event', 'reminder')
  - `status` ('scheduled', 'completed', 'cancelled')
  - `priority` ('low', 'medium', 'high')
  - `allDay` (boolean)
  - `isRecurring`, `recurringPattern` (optional)
  - `reminder` (minutes before event)
  - `color` (hex color)
  - `customerId`, `contactId`, `dealId`, `taskId`, `assignedToId` (optional)

#### PUT /calendar/:id
Update calendar event.
- **Auth**: Required
- **Params**: `id` (UUID)
- **Body**: Same as create (all optional)

#### DELETE /calendar/:id
Delete calendar event.
- **Auth**: Required (Creator or Admin only)
- **Params**: `id` (UUID)

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "errors": [
    {
      "field": "email",
      "message": "Please provide a valid email"
    }
  ]
}
```

### 401 Unauthorized
```json
{
  "success": false,
  "error": "Not authorized"
}
```

### 403 Forbidden
```json
{
  "success": false,
  "error": "User role agent is not authorized to access this route"
}
```

### 404 Not Found
```json
{
  "success": false,
  "error": "Resource not found"
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "error": "Server Error"
}
```

## Rate Limiting
- 100 requests per 10 minutes per IP address
- Applies to all `/api` endpoints

## CORS
- Configured via `CORS_ORIGIN` environment variable
- Supports multiple origins (comma-separated)

## File Uploads
- Static files served from `/uploads`
- Use multipart/form-data for file uploads (implementation pending)

/* Modern UI Enhancements for CRM System */

/* Professional Color Scheme */
:root {
    --primary-color: #1e40af;
    --primary-hover: #1d4ed8;
    --secondary-color: #475569;
    --success-color: #059669;
    --danger-color: #dc2626;
    --warning-color: #d97706;
    --info-color: #0891b2;
    --light-bg: #f8fafc;
    --card-bg: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --transition-all: all 0.2s ease;
}

/* Modern Typography */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    background: var(--light-bg);
    color: var(--text-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Professional Header */
header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    box-shadow: var(--shadow-md);
    border-radius: 0;
    margin-bottom: 30px;
    padding: 20px 40px;
}

.logo h1 {
    font-size: 1.875rem;
    font-weight: 700;
    letter-spacing: -0.025em;
}

/* Modern Navigation */
nav {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    margin-bottom: 30px;
    overflow: hidden;
}

nav ul {
    gap: 1px;
    background: var(--border-color);
}

nav ul li a {
    background: var(--card-bg);
    color: var(--text-primary);
    font-weight: 500;
    padding: 16px 24px;
    position: relative;
    transition: var(--transition-all);
}

nav ul li a:hover {
    background: var(--light-bg);
    color: var(--primary-color);
}

nav ul li a.active {
    background: var(--primary-color);
    color: white;
}

nav ul li a.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: white;
}

/* Modern Cards */
main {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
    border-radius: 12px;
    padding: 32px;
}

.card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-all);
}

.card:hover {
    box-shadow: var(--shadow-md);
}

/* Modern Forms */
.form-group input,
.form-group select,
.form-group textarea {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 10px 14px;
    font-size: 14px;
    transition: var(--transition-all);
    background: var(--card-bg);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(30 64 175 / 0.1);
}

/* Modern Buttons */
button,
.btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 14px;
    border: none;
    cursor: pointer;
    transition: var(--transition-all);
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-outline {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.btn-outline:hover {
    background: var(--light-bg);
}

/* Modern Tables */
table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
}

th {
    background: var(--light-bg);
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
}

td {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    font-size: 14px;
}

tr:hover {
    background: var(--light-bg);
}

tr:last-child td {
    border-bottom: none;
}

/* Status Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 9999px;
    font-size: 12px;
    font-weight: 500;
}

.badge-success {
    background: #d1fae5;
    color: #065f46;
}

.badge-warning {
    background: #fed7aa;
    color: #92400e;
}

.badge-danger {
    background: #fee2e2;
    color: #991b1b;
}

.badge-info {
    background: #dbeafe;
    color: #1e40af;
}

/* Modern Dashboard Stats */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.stat-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 24px;
    box-shadow: var(--shadow-sm);
}

.stat-card h3 {
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 8px 0;
}

.stat-card .stat-value {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.stat-card .stat-change {
    font-size: 14px;
    margin-top: 8px;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

/* Modern Search Bar */
.search-container {
    position: relative;
    margin-bottom: 24px;
}

.search-container input {
    width: 100%;
    padding: 12px 16px 12px 44px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 14px;
}

.search-container::before {
    content: "🔍";
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

/* Modern Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: var(--card-bg);
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    padding: 24px;
    border-bottom: 1px solid var(--border-color);
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* Responsive Design */
@media (max-width: 768px) {
    header {
        padding: 16px 20px;
    }
    
    nav ul {
        flex-direction: column;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    main {
        padding: 20px;
    }
}

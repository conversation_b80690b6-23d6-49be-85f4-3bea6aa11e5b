const { Contact, Customer, User, Activity } = require('../models');
const { Op } = require('sequelize');

// @desc    Get all contacts
// @route   GET /api/contacts
// @access  Private
exports.getContacts = async (req, res, next) => {
  try {
    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 25;
    const offset = (page - 1) * limit;

    // Filtering
    const where = {};
    
    if (req.query.customerId) {
      where.customerId = req.query.customerId;
    }
    
    if (req.query.isPrimary !== undefined) {
      where.isPrimary = req.query.isPrimary === 'true';
    }
    
    if (req.query.search) {
      where[Op.or] = [
        { firstName: { [Op.iLike]: `%${req.query.search}%` } },
        { lastName: { [Op.iLike]: `%${req.query.search}%` } },
        { email: { [Op.iLike]: `%${req.query.search}%` } },
        { jobTitle: { [Op.iLike]: `%${req.query.search}%` } }
      ];
    }

    // Sorting
    const order = [];
    if (req.query.sortBy) {
      const sortParts = req.query.sortBy.split(':');
      order.push([sortParts[0], sortParts[1] ? sortParts[1].toUpperCase() : 'ASC']);
    } else {
      order.push(['createdAt', 'DESC']);
    }

    const { count, rows } = await Contact.findAndCountAll({
      where,
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'companyName']
        },
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'firstName', 'lastName']
        }
      ],
      order,
      limit,
      offset,
      distinct: true
    });

    res.status(200).json({
      success: true,
      count,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(count / limit)
      },
      data: rows
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single contact
// @route   GET /api/contacts/:id
// @access  Private
exports.getContact = async (req, res, next) => {
  try {
    const contact = await Contact.findByPk(req.params.id, {
      include: [
        {
          model: Customer,
          as: 'customer'
        },
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'firstName', 'lastName']
        },
        {
          model: Activity,
          as: 'activities',
          limit: 10,
          order: [['activityDate', 'DESC']],
          include: [
            {
              model: User,
              as: 'performedBy',
              attributes: ['id', 'firstName', 'lastName']
            }
          ]
        }
      ]
    });

    if (!contact) {
      return res.status(404).json({
        success: false,
        error: 'Contact not found'
      });
    }

    res.status(200).json({
      success: true,
      data: contact
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create new contact
// @route   POST /api/contacts
// @access  Private
exports.createContact = async (req, res, next) => {
  try {
    req.body.createdById = req.user.id;
    
    const contact = await Contact.create(req.body);

    // Log activity
    await Activity.create({
      type: 'contact_created',
      subject: 'New contact created',
      description: `Contact ${contact.firstName} ${contact.lastName} was created`,
      entityType: 'contact',
      entityId: contact.id,
      customerId: contact.customerId,
      contactId: contact.id,
      performedById: req.user.id
    });

    // Load with associations
    const contactWithAssociations = await Contact.findByPk(contact.id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'companyName']
        }
      ]
    });

    res.status(201).json({
      success: true,
      data: contactWithAssociations
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update contact
// @route   PUT /api/contacts/:id
// @access  Private
exports.updateContact = async (req, res, next) => {
  try {
    let contact = await Contact.findByPk(req.params.id);

    if (!contact) {
      return res.status(404).json({
        success: false,
        error: 'Contact not found'
      });
    }

    contact = await contact.update(req.body);

    res.status(200).json({
      success: true,
      data: contact
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete contact
// @route   DELETE /api/contacts/:id
// @access  Private (Admin/Manager)
exports.deleteContact = async (req, res, next) => {
  try {
    const contact = await Contact.findByPk(req.params.id);

    if (!contact) {
      return res.status(404).json({
        success: false,
        error: 'Contact not found'
      });
    }

    await contact.destroy();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

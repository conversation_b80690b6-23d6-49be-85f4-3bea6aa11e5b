<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM System - Deals</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/bootstrap-custom.css">
    <style>
        .kanban-board {
            display: flex;
            gap: 1rem;
            overflow-x: auto;
            padding: 1rem 0;
        }
        .kanban-column {
            flex: 0 0 300px;
            background: var(--bg-secondary);
            border-radius: 0.5rem;
            padding: 1rem;
        }
        .kanban-column h5 {
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            color: var(--text-secondary);
            margin-bottom: 1rem;
        }
        .kanban-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 0.75rem;
            cursor: move;
            transition: all 0.2s ease;
        }
        .kanban-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }
        .kanban-card-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        .kanban-card-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        .kanban-card-customer {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
        .stage-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .stage-prospecting { background: #e0e7ff; color: #4338ca; }
        .stage-qualification { background: #dbeafe; color: #1e40af; }
        .stage-proposal { background: #fef3c7; color: #92400e; }
        .stage-negotiation { background: #fed7aa; color: #c2410c; }
        .stage-closed-won { background: #d1fae5; color: #065f46; }
        .stage-closed-lost { background: #fee2e2; color: #991b1b; }
    </style>
</head>
<body>
    <!-- Navigation Container -->
    <div id="navigationContainer"></div>
    
    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row mb-4">
            <div class="col">
                <h2 class="mb-0">Deal Management</h2>
                <p class="text-muted">Track and manage your sales pipeline</p>
            </div>
            <div class="col-auto">
                <div class="btn-group" role="group">
                    <button id="tableViewBtn" class="btn btn-outline-primary active">
                        <i class="bi bi-table me-1"></i>Table View
                    </button>
                    <button id="kanbanViewBtn" class="btn btn-outline-primary">
                        <i class="bi bi-kanban me-1"></i>Kanban Board
                    </button>
                </div>
                <button id="addDealBtn" class="btn btn-primary ms-2" data-bs-toggle="modal" data-bs-target="#dealFormModal">
                    <i class="bi bi-plus-circle me-1"></i>Add New Deal
                </button>
            </div>
        </div>
        
        <!-- Deal Stats -->
        <div class="row g-3 mb-4">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="text-muted mb-2">Total Pipeline Value</h6>
                        <h3 class="mb-0" id="totalPipelineValue">$0</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="text-muted mb-2">Average Deal Size</h6>
                        <h3 class="mb-0" id="avgDealSize">$0</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="text-muted mb-2">Win Rate</h6>
                        <h3 class="mb-0" id="winRate">0%</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="text-muted mb-2">Active Deals</h6>
                        <h3 class="mb-0" id="activeDealsCount">0</h3>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Table View -->
        <div class="card" id="dealsTableContainer">
            <div class="card-body">
                <div class="table-responsive">
                    <table id="dealsTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Deal Name</th>
                                <th>Customer</th>
                                <th>Value</th>
                                <th>Stage</th>
                                <th>Close Date</th>
                                <th>Owner</th>
                                <th>Win %</th>
                                <th class="no-sort">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="dealsTableBody">
                            <!-- Sample data for demonstration -->
                            <tr>
                                <td>Enterprise Software Deal</td>
                                <td>Acme Corp</td>
                                <td>$50,000</td>
                                <td><span class="stage-badge stage-negotiation">Negotiation</span></td>
                                <td>2024-02-15</td>
                                <td>John Doe</td>
                                <td>75%</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary">Edit</button>
                                        <button class="btn btn-outline-secondary">Notes</button>
                                        <button class="btn btn-outline-danger">Delete</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Kanban View -->
        <div id="kanbanBoard" style="display: none;">
            <div class="kanban-board">
                <div class="kanban-column">
                    <h5>Prospecting</h5>
                    <div class="kanban-list" id="kanban-prospecting">
                        <!-- Kanban cards will be populated here -->
                    </div>
                </div>
                <div class="kanban-column">
                    <h5>Qualification</h5>
                    <div class="kanban-list" id="kanban-qualification">
                        <!-- Kanban cards will be populated here -->
                    </div>
                </div>
                <div class="kanban-column">
                    <h5>Proposal</h5>
                    <div class="kanban-list" id="kanban-proposal">
                        <!-- Kanban cards will be populated here -->
                    </div>
                </div>
                <div class="kanban-column">
                    <h5>Negotiation</h5>
                    <div class="kanban-list" id="kanban-negotiation">
                        <!-- Kanban cards will be populated here -->
                    </div>
                </div>
                <div class="kanban-column">
                    <h5>Closed Won</h5>
                    <div class="kanban-list" id="kanban-closed-won">
                        <!-- Kanban cards will be populated here -->
                    </div>
                </div>
                <div class="kanban-column">
                    <h5>Closed Lost</h5>
                    <div class="kanban-list" id="kanban-closed-lost">
                        <!-- Kanban cards will be populated here -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Deal Form Modal -->
        <div class="modal fade" id="dealFormModal" tabindex="-1" aria-labelledby="dealFormModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="dealFormModalLabel">Add New Deal</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="dealForm" class="needs-validation" novalidate>
                            <input type="hidden" id="dealId">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="dealTitle" class="form-label">Deal Title <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="dealTitle" name="dealTitle" required>
                                    <div class="invalid-feedback">
                                        Please provide a deal title.
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="dealValue" class="form-label">Deal Value <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="dealValue" name="dealValue" required>
                                    </div>
                                    <div class="invalid-feedback">
                                        Please provide a deal value.
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="dealCustomer" class="form-label">Customer <span class="text-danger">*</span></label>
                                    <select class="form-select" id="dealCustomer" name="dealCustomer" required>
                                        <option value="">Choose customer...</option>
                                        <!-- Customer options will be populated here -->
                                    </select>
                                    <div class="invalid-feedback">
                                        Please select a customer.
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="dealStage" class="form-label">Stage <span class="text-danger">*</span></label>
                                    <select class="form-select" id="dealStage" name="dealStage" required>
                                        <option value="Prospecting">Prospecting</option>
                                        <option value="Qualification">Qualification</option>
                                        <option value="Proposal">Proposal</option>
                                        <option value="Negotiation">Negotiation</option>
                                        <option value="Closed Won">Closed Won</option>
                                        <option value="Closed Lost">Closed Lost</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="dealCloseDate" class="form-label">Expected Close Date <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="dealCloseDate" name="dealCloseDate" required>
                                    <div class="invalid-feedback">
                                        Please provide an expected close date.
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="dealProbability" class="form-label">Win Probability (%)</label>
                                    <input type="number" class="form-control" id="dealProbability" name="dealProbability" min="0" max="100" value="50">
                                </div>
                                <div class="col-12">
                                    <label for="dealDescription" class="form-label">Description</label>
                                    <textarea class="form-control" id="dealDescription" name="dealDescription" rows="3"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" form="dealForm" class="btn btn-primary">Save Deal</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Deal Notes Modal -->
        <div class="modal fade" id="dealNotesModal" tabindex="-1" aria-labelledby="dealNotesModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="dealNotesModalLabel">Deal Notes</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div id="dealNotesList" class="mb-3">
                            <!-- Notes will be populated here -->
                        </div>
                        <div class="mb-3">
                            <label for="newDealNoteText" class="form-label">Add a Note</label>
                            <textarea id="newDealNoteText" class="form-control" rows="3" placeholder="Add a note..."></textarea>
                        </div>
                        <button id="saveDealNoteBtn" class="btn btn-primary">Save Note</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap 5 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom Scripts -->
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/common-ui.js"></script>
    <script src="js/table-utils.js"></script>
    <script src="js/deals.js"></script>
    <script>
        // Initialize common UI
        document.addEventListener('DOMContentLoaded', function() {
            commonUI.initializeCommonUI('deals');
            
            // Initialize table with sorting and filtering
            if (window.tableUtils) {
                tableUtils.initTable('dealsTable', {
                    sortable: true,
                    filterable: true,
                    paginate: true,
                    pageSize: 10
                });
            }
            
            // Toggle between table and kanban views
            document.getElementById('tableViewBtn').addEventListener('click', function() {
                document.getElementById('dealsTableContainer').style.display = 'block';
                document.getElementById('kanbanBoard').style.display = 'none';
                this.classList.add('active');
                document.getElementById('kanbanViewBtn').classList.remove('active');
            });
            
            document.getElementById('kanbanViewBtn').addEventListener('click', function() {
                document.getElementById('dealsTableContainer').style.display = 'none';
                document.getElementById('kanbanBoard').style.display = 'block';
                this.classList.add('active');
                document.getElementById('tableViewBtn').classList.remove('active');
            });
            
            // Bootstrap form validation
            const forms = document.querySelectorAll('.needs-validation');
            Array.from(forms).forEach(form => {
                form.addEventListener('submit', event => {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM System - Customers</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/bootstrap-custom.css">
</head>
<body>
    <!-- Navigation Container -->
    <div id="navigationContainer"></div>
    <div class="container-fluid mt-4">
        <div class="row mb-4">
            <div class="col">
                <h2 class="mb-0">Customer Management</h2>
                <p class="text-muted">Manage and view all customer interactions and info.</p>
            </div>
            <div class="col-auto">
                <div class="d-flex gap-2">
                    <div class="input-group">
                        <input type="text" id="customerSearch" class="form-control" placeholder="Search customers...">
                        <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                    <button id="addCustomerBtn" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>Add New Customer
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Message Container -->
        <div id="messageContainer"></div>

        <!-- Customer Table -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table id="customersTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Company</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="customersTableBody">
                            <!-- Customer rows will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Modals -->
        <div id="notesModal" class="modal fade" tabindex="-1" aria-labelledby="notesModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="notesModalLabel">Customer Notes</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div id="notesList"></div>
                        <div class="mb-3">
                            <label for="newNoteText" class="form-label">Add a Note</label>
                            <textarea id="newNoteText" rows="3" class="form-control" placeholder="Add a note..."></textarea>
                        </div>
                        <button id="saveNoteBtn" class="btn btn-primary">Save Note</button>
                    </div>
                </div>
            </div>
        </div>

        <div id="attachmentsModal" class="modal fade" tabindex="-1" aria-labelledby="attachmentsModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="attachmentsModalLabel">Customer Attachments</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <input type="file" id="attachmentInput" class="form-control" multiple>
                        <button id="uploadAttachmentBtn" class="btn btn-primary mt-3">Upload</button>
                        <div id="attachmentsList"></div>
                    </div>
                </div>
            </div>
        </div>

        <div id="timelineModal" class="modal fade" tabindex="-1" aria-labelledby="timelineModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="timelineModalLabel">Customer Activity Timeline</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div id="timelineList"></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Customer Form Modal -->
        <div class="modal fade" id="customerFormModal" tabindex="-1" aria-labelledby="customerFormModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="customerFormModalLabel">Add New Customer</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div id="formMessage"></div>
                        <form id="customerForm" class="needs-validation" novalidate>
                            <input type="hidden" id="customerId">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="customerName" class="form-label">Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="customerName" name="customerName" required>
                                    <div class="invalid-feedback">
                                        Please provide a valid name.
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="customerEmail" class="form-label">Email <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="customerEmail" name="customerEmail" required>
                                    <div class="invalid-feedback">
                                        Please provide a valid email address.
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="customerPhone" class="form-label">Phone</label>
                                    <input type="tel" class="form-control" id="customerPhone" name="customerPhone">
                                </div>
                                <div class="col-md-6">
                                    <label for="customerCompany" class="form-label">Company</label>
                                    <input type="text" class="form-control" id="customerCompany" name="customerCompany">
                                </div>
                                <div class="col-12">
                                    <label for="customerTags" class="form-label">Tags</label>
                                    <input type="text" class="form-control" id="customerTags" name="customerTags" placeholder="e.g. VIP, Lead, Prospect">
                                    <div class="form-text">Separate multiple tags with commas</div>
                                </div>
                                <div class="col-12">
                                    <label for="customerAddress" class="form-label">Address</label>
                                    <textarea class="form-control" id="customerAddress" name="customerAddress" rows="3"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" form="customerForm" class="btn btn-primary">Save Customer</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom Scripts -->
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/common-ui.js"></script>
    <script src="js/table-utils.js"></script>
    <script src="js/customers.js"></script>
    <script>
        // Initialize common UI
        document.addEventListener('DOMContentLoaded', function() {
            commonUI.initializeCommonUI('customers');
            
            // Initialize table with sorting and filtering
            if (window.tableUtils) {
                tableUtils.initTable('customersTable', {
                    sortable: true,
                    filterable: true,
                    paginate: true,
                    pageSize: 10
                });
            }
        });
    </script>
</body>
</html>

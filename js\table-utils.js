// Table Sorting and Filtering Utilities

class TableUtils {
    constructor() {
        this.tables = new Map();
    }

    // Initialize sortable and filterable table
    initTable(tableId, options = {}) {
        const table = document.getElementById(tableId);
        if (!table) return;

        const config = {
            sortable: true,
            filterable: true,
            paginate: true,
            pageSize: 10,
            ...options
        };

        this.tables.set(tableId, {
            table,
            config,
            data: this.extractTableData(table),
            currentSort: { column: null, direction: 'asc' },
            currentPage: 1,
            filters: {}
        });

        if (config.sortable) {
            this.addSortingToHeaders(tableId);
        }

        if (config.filterable) {
            this.addFilterRow(tableId);
        }

        if (config.paginate) {
            this.addPagination(tableId);
        }
    }

    // Extract table data for manipulation
    extractTableData(table) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        return rows.map(row => {
            const cells = Array.from(row.querySelectorAll('td'));
            return {
                element: row,
                data: cells.map(cell => cell.textContent.trim()),
                visible: true
            };
        });
    }

    // Add sorting functionality to headers
    addSortingToHeaders(tableId) {
        const { table } = this.tables.get(tableId);
        const headers = table.querySelectorAll('thead th');

        headers.forEach((header, index) => {
            if (!header.classList.contains('no-sort')) {
                header.classList.add('sortable');
                header.addEventListener('click', () => this.sortTable(tableId, index));
            }
        });
    }

    // Sort table by column
    sortTable(tableId, columnIndex) {
        const tableData = this.tables.get(tableId);
        const { data, currentSort } = tableData;
        
        // Determine sort direction
        let direction = 'asc';
        if (currentSort.column === columnIndex && currentSort.direction === 'asc') {
            direction = 'desc';
        }

        // Update sort classes
        const headers = tableData.table.querySelectorAll('thead th');
        headers.forEach(h => h.classList.remove('asc', 'desc'));
        headers[columnIndex].classList.add(direction);

        // Sort data
        data.sort((a, b) => {
            const aVal = a.data[columnIndex];
            const bVal = b.data[columnIndex];
            
            // Try to parse as number
            const aNum = parseFloat(aVal);
            const bNum = parseFloat(bVal);
            
            if (!isNaN(aNum) && !isNaN(bNum)) {
                return direction === 'asc' ? aNum - bNum : bNum - aNum;
            }
            
            // Sort as string
            if (direction === 'asc') {
                return aVal.localeCompare(bVal);
            } else {
                return bVal.localeCompare(aVal);
            }
        });

        tableData.currentSort = { column: columnIndex, direction };
        this.renderTable(tableId);
    }

    // Add filter row to table
    addFilterRow(tableId) {
        const { table } = this.tables.get(tableId);
        const thead = table.querySelector('thead');
        const headerRow = thead.querySelector('tr');
        const filterRow = document.createElement('tr');
        filterRow.className = 'filter-row';

        const headers = headerRow.querySelectorAll('th');
        headers.forEach((header, index) => {
            const filterCell = document.createElement('th');
            filterCell.className = 'p-2';
            
            if (!header.classList.contains('no-filter')) {
                const input = document.createElement('input');
                input.type = 'text';
                input.className = 'form-control form-control-sm';
                input.placeholder = 'Filter...';
                input.addEventListener('input', (e) => this.filterTable(tableId, index, e.target.value));
                filterCell.appendChild(input);
            }
            
            filterRow.appendChild(filterCell);
        });

        thead.appendChild(filterRow);
    }

    // Filter table by column
    filterTable(tableId, columnIndex, filterValue) {
        const tableData = this.tables.get(tableId);
        
        if (filterValue) {
            tableData.filters[columnIndex] = filterValue.toLowerCase();
        } else {
            delete tableData.filters[columnIndex];
        }

        // Apply all filters
        tableData.data.forEach(row => {
            row.visible = true;
            
            for (const [colIndex, filter] of Object.entries(tableData.filters)) {
                if (!row.data[colIndex].toLowerCase().includes(filter)) {
                    row.visible = false;
                    break;
                }
            }
        });

        tableData.currentPage = 1;
        this.renderTable(tableId);
    }

    // Add pagination controls
    addPagination(tableId) {
        const { table, config } = this.tables.get(tableId);
        
        const paginationContainer = document.createElement('nav');
        paginationContainer.className = 'mt-3';
        paginationContainer.innerHTML = `
            <ul class="pagination justify-content-center" id="${tableId}-pagination">
            </ul>
        `;
        
        table.parentNode.insertBefore(paginationContainer, table.nextSibling);
        this.updatePagination(tableId);
    }

    // Update pagination controls
    updatePagination(tableId) {
        const tableData = this.tables.get(tableId);
        const { data, config, currentPage } = tableData;
        
        const visibleRows = data.filter(row => row.visible);
        const totalPages = Math.ceil(visibleRows.length / config.pageSize);
        
        const paginationEl = document.getElementById(`${tableId}-pagination`);
        paginationEl.innerHTML = '';

        // Previous button
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
        prevLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage - 1}">Previous</a>`;
        paginationEl.appendChild(prevLi);

        // Page numbers
        const maxButtons = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxButtons / 2));
        let endPage = Math.min(totalPages, startPage + maxButtons - 1);
        
        if (endPage - startPage < maxButtons - 1) {
            startPage = Math.max(1, endPage - maxButtons + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            const li = document.createElement('li');
            li.className = `page-item ${i === currentPage ? 'active' : ''}`;
            li.innerHTML = `<a class="page-link" href="#" data-page="${i}">${i}</a>`;
            paginationEl.appendChild(li);
        }

        // Next button
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
        nextLi.innerHTML = `<a class="page-link" href="#" data-page="${currentPage + 1}">Next</a>`;
        paginationEl.appendChild(nextLi);

        // Add click handlers
        paginationEl.querySelectorAll('.page-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = parseInt(e.target.getAttribute('data-page'));
                if (page >= 1 && page <= totalPages) {
                    tableData.currentPage = page;
                    this.renderTable(tableId);
                }
            });
        });
    }

    // Render table with current sorting, filtering, and pagination
    renderTable(tableId) {
        const tableData = this.tables.get(tableId);
        const { data, config, currentPage } = tableData;
        const tbody = tableData.table.querySelector('tbody');
        
        // Clear tbody
        tbody.innerHTML = '';
        
        // Get visible rows
        const visibleRows = data.filter(row => row.visible);
        
        // Calculate pagination
        const startIndex = (currentPage - 1) * config.pageSize;
        const endIndex = Math.min(startIndex + config.pageSize, visibleRows.length);
        
        // Render rows
        for (let i = startIndex; i < endIndex; i++) {
            tbody.appendChild(visibleRows[i].element);
        }
        
        // Update pagination
        if (config.paginate) {
            this.updatePagination(tableId);
        }
    }

    // Export table data
    exportTable(tableId, format = 'csv') {
        const { data } = this.tables.get(tableId);
        const visibleData = data.filter(row => row.visible);
        
        if (format === 'csv') {
            const headers = Array.from(this.tables.get(tableId).table.querySelectorAll('thead th:not(.filter-row th)'))
                .map(th => th.textContent.trim());
            
            let csv = headers.join(',') + '\n';
            visibleData.forEach(row => {
                csv += row.data.map(cell => `"${cell.replace(/"/g, '""')}"`).join(',') + '\n';
            });
            
            const blob = new Blob([csv], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${tableId}-export.csv`;
            a.click();
            URL.revokeObjectURL(url);
        }
    }
}

// Initialize global instance
window.tableUtils = new TableUtils();

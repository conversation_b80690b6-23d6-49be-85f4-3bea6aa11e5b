const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const path = require('path');
const cookieParser = require('cookie-parser');
const session = require('express-session');
const MongoStore = require('connect-mongo');

// Load env vars
dotenv.config();

// Route files
const auth = require('./routes/auth');
const customers = require('./routes/customers');
const contacts = require('./routes/contacts');
const deals = require('./routes/deals');
const tasks = require('./routes/tasks');
const calendar = require('./routes/calendar');
const activities = require('./routes/activities');
const users = require('./routes/users');
const dashboard = require('./routes/dashboard');

// Import database and models
const { sequelize } = require('./models');
const { testConnection } = require('./config/database');

// Import custom middleware
const errorHandler = require('./middleware/errorHandler');
const { 
  generalLimiter, 
  authLimiter, 
  createAccountLimiter,
  uploadLimiter,
  reportLimiter 
} = require('./middleware/rateLimiter');
const { globalSanitizer, sanitizeBody, schemas } = require('./middleware/sanitizer');
const { doubleSubmitCookie, csrfTokenEndpoint } = require('./middleware/csrfProtection');
const { 
  requestId, 
  requestContext, 
  morganMiddleware,
  logRequestDetails,
  logResponseDetails,
  errorLogger
} = require('./middleware/requestLogger');

const app = express();

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Request tracking and logging
app.use(requestId);
app.use(requestContext);
app.use(morganMiddleware);

// Cookie parser
app.use(cookieParser());

// Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET || 'default-session-secret',
  resave: false,
  saveUninitialized: false,
  store: process.env.MONGO_URI ? MongoStore.create({
    mongoUrl: process.env.MONGO_URI
  }) : undefined,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Body parser with size limits
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Global input sanitization
app.use(globalSanitizer);

// Security headers with Helmet
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://fonts.googleapis.com"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https://cdn.jsdelivr.net"],
      imgSrc: ["'self'", "data:", "https:"],
      fontSrc: ["'self'", "data:", "https://fonts.gstatic.com"],
      connectSrc: ["'self'"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"]
    }
  },
  hsts: {
    maxAge: parseInt(process.env.HSTS_MAX_AGE) || 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// CORS configuration
app.use(cors({
  origin: function(origin, callback) {
    const allowedOrigins = process.env.CORS_ORIGIN.split(',').map(o => o.trim());
    if (!origin || allowedOrigins.includes(origin) || origin.startsWith('file://')) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token', 'X-Request-ID'],
  exposedHeaders: ['X-Request-ID']
}));

// Compression middleware
if (process.env.ENABLE_COMPRESSION === 'true') {
  app.use(compression());
}

// CSRF Protection
app.use(doubleSubmitCookie());

// CSRF token endpoint
app.get('/api/csrf-token', csrfTokenEndpoint);

// General rate limiting for all API routes
app.use('/api', generalLimiter);

// Specific rate limiting for auth routes
app.use('/api/auth/login', authLimiter);
app.use('/api/auth/register', createAccountLimiter);
app.use('/api/auth/forgot-password', authLimiter);
app.use('/api/auth/reset-password', authLimiter);

// File upload rate limiting
app.use('/api/upload', uploadLimiter);
app.use('/api/*/upload', uploadLimiter);

// Report generation rate limiting
app.use('/api/reports', reportLimiter);
app.use('/api/*/export', reportLimiter);

// Set static folder for uploads with security headers
app.use('/uploads', express.static(path.join(__dirname, 'uploads'), {
  setHeaders: (res, path) => {
    res.set('X-Content-Type-Options', 'nosniff');
    res.set('X-Frame-Options', 'DENY');
  }
}));

// Request logging for debugging
if (process.env.NODE_ENV === 'development') {
  app.use(logRequestDetails);
}

// Mount routers with specific sanitization
app.use('/api/auth', sanitizeBody(schemas.user), auth);
app.use('/api/customers', sanitizeBody(schemas.customer), customers);
app.use('/api/contacts', sanitizeBody(schemas.customer), contacts);
app.use('/api/deals', sanitizeBody(schemas.deal), deals);
app.use('/api/tasks', sanitizeBody(schemas.task), tasks);
app.use('/api/calendar', calendar);
app.use('/api/activities', activities);
app.use('/api/users', sanitizeBody(schemas.user), users);
app.use('/api/dashboard', dashboard);

// Health check endpoint (no auth required)
app.get('/api/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'CRM API is running',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'Resource not found',
    path: req.originalUrl
  });
});

// Response logging
app.use(logResponseDetails);

// Error logging
app.use(errorLogger);

// Error handler
app.use(errorHandler);

const PORT = process.env.PORT || 5000;

// Initialize database and start server
const startServer = async () => {
  try {
    // Test database connection
    await testConnection();
    
    // Sync database models
    await sequelize.sync({ alter: true });
    console.log('Database synchronized');

    app.listen(PORT, () => {
      console.log(`Server is running in ${process.env.NODE_ENV} mode on port ${PORT}`);
    });
  } catch (error) {
    console.error('Unable to start server:', error);
    process.exit(1);
  }
};

startServer();

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  console.log(`Error: ${err.message}`);
  // Close server & exit process
  process.exit(1);
});

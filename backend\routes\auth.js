/**
 * @module routes/auth
 * @description Authentication routes for user registration, login, and profile management
 */

const express = require('express');
const { register, login, getMe, updateDetails, updatePassword } = require('../controllers/authController');
const { protect } = require('../middleware/auth');
const { 
  validateEmail, 
  validateRequired, 
  handleValidationErrors,
  body 
} = require('../middleware/validation');
const asyncHandler = require('../middleware/asyncHandler');

const router = express.Router();

// Validation rules
const validateRegister = [
  validateRequired('firstName', 'First name is required'),
  validateRequired('lastName', 'Last name is required'),
  validateEmail(),
  body('password')
    .isLength({ min: 6 }).withMessage('Password must be at least 6 characters')
    .matches(/\d/).withMessage('Password must contain at least one number'),
  body('role')
    .optional()
    .isIn(['admin', 'manager', 'agent']).withMessage('Invalid role')
];

const validateLogin = [
  validateEmail(),
  validateRequired('password', 'Password is required')
];

const validateUpdatePassword = [
  validateRequired('currentPassword', 'Current password is required'),
  body('newPassword')
    .isLength({ min: 6 }).withMessage('New password must be at least 6 characters')
    .matches(/\d/).withMessage('New password must contain at least one number')
];

/**
 * @route POST /api/auth/register
 * @description Register a new user
 * @access Public
 * @body {Object} req.body
 * @body {string} req.body.firstName - User's first name (required)
 * @body {string} req.body.lastName - User's last name (required)
 * @body {string} req.body.email - User's email address (required, must be valid email)
 * @body {string} req.body.password - User's password (required, min 6 chars, must contain number)
 * @body {string} [req.body.role] - User role (optional, defaults to 'agent', enum: ['admin', 'manager', 'agent'])
 * @returns {Object} 201 - Success response with token and user data
 * @returns {Object} 400 - Validation error
 * @returns {Object} 409 - Email already exists
 * @example
 * POST /api/auth/register
 * {
 *   "firstName": "John",
 *   "lastName": "Doe",
 *   "email": "<EMAIL>",
 *   "password": "securePassword123",
 *   "role": "agent"
 * }
 */
router.post('/register', validateRegister, handleValidationErrors, asyncHandler(register));

/**
 * @route POST /api/auth/login
 * @description Authenticate user and get token
 * @access Public
 * @body {Object} req.body
 * @body {string} req.body.email - User's email address (required)
 * @body {string} req.body.password - User's password (required)
 * @returns {Object} 200 - Success response with token and user data
 * @returns {Object} 400 - Validation error
 * @returns {Object} 401 - Invalid credentials
 * @example
 * POST /api/auth/login
 * {
 *   "email": "<EMAIL>",
 *   "password": "securePassword123"
 * }
 */
router.post('/login', validateLogin, handleValidationErrors, asyncHandler(login));

/**
 * @route GET /api/auth/me
 * @description Get current logged in user
 * @access Private
 * @header {string} Authorization - Bearer token (required)
 * @returns {Object} 200 - Success response with user data
 * @returns {Object} 401 - Not authorized
 * @example
 * GET /api/auth/me
 * Headers: {
 *   "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 * }
 */
router.get('/me', protect, asyncHandler(getMe));

/**
 * @route PUT /api/auth/updatedetails
 * @description Update user details
 * @access Private
 * @header {string} Authorization - Bearer token (required)
 * @body {Object} req.body
 * @body {string} [req.body.firstName] - User's first name (optional)
 * @body {string} [req.body.lastName] - User's last name (optional)
 * @body {string} [req.body.email] - User's email address (optional, must be valid email)
 * @returns {Object} 200 - Success response with updated user data
 * @returns {Object} 400 - Validation error
 * @returns {Object} 401 - Not authorized
 * @example
 * PUT /api/auth/updatedetails
 * Headers: {
 *   "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 * }
 * Body: {
 *   "firstName": "Jane",
 *   "email": "<EMAIL>"
 * }
 */
router.put('/updatedetails', protect, [
  body('firstName').optional().trim().notEmpty().withMessage('First name cannot be empty'),
  body('lastName').optional().trim().notEmpty().withMessage('Last name cannot be empty'),
  body('email').optional().isEmail().withMessage('Please provide a valid email').normalizeEmail()
], handleValidationErrors, asyncHandler(updateDetails));

/**
 * @route PUT /api/auth/updatepassword
 * @description Update password
 * @access Private
 * @header {string} Authorization - Bearer token (required)
 * @body {Object} req.body
 * @body {string} req.body.currentPassword - Current password (required)
 * @body {string} req.body.newPassword - New password (required, min 6 chars, must contain number)
 * @returns {Object} 200 - Success response with new token
 * @returns {Object} 400 - Validation error
 * @returns {Object} 401 - Invalid current password
 * @example
 * PUT /api/auth/updatepassword
 * Headers: {
 *   "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 * }
 * Body: {
 *   "currentPassword": "oldPassword123",
 *   "newPassword": "newSecurePassword456"
 * }
 */
router.put('/updatepassword', protect, validateUpdatePassword, handleValidationErrors, asyncHandler(updatePassword));

module.exports = router;

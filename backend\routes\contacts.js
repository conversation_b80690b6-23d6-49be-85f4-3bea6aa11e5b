const express = require('express');
const {
  getContacts,
  getContact,
  createContact,
  updateContact,
  deleteContact
} = require('../controllers/contactController');

const { protect, authorize } = require('../middleware/auth');
const asyncHandler = require('../middleware/asyncHandler');
const {
  validateUUID,
  validateEmail,
  validatePhone,
  validateRequired,
  validateOptional,
  validatePagination,
  handleValidationErrors,
  body,
  query
} = require('../middleware/validation');

const router = express.Router();

// Validation rules
const validateCreateContact = [
  validateRequired('firstName', 'First name is required'),
  validateRequired('lastName', 'Last name is required'),
  validateEmail(),
  validatePhone('phone'),
  validatePhone('mobile'),
  validateOptional('jobTitle'),
  validateOptional('department'),
  body('isPrimary')
    .optional()
    .isBoolean().withMessage('isPrimary must be a boolean'),
  body('birthday')
    .optional()
    .isISO8601().withMessage('Birthday must be a valid date'),
  body('customerId')
    .notEmpty().withMessage('Customer ID is required')
    .isUUID().withMessage('Customer ID must be a valid UUID')
];

const validateUpdateContact = [
  validateOptional('firstName'),
  validateOptional('lastName'),
  body('email')
    .optional()
    .isEmail().withMessage('Please provide a valid email')
    .normalizeEmail(),
  validatePhone('phone'),
  validatePhone('mobile'),
  validateOptional('jobTitle'),
  validateOptional('department'),
  body('isPrimary')
    .optional()
    .isBoolean().withMessage('isPrimary must be a boolean'),
  body('birthday')
    .optional()
    .isISO8601().withMessage('Birthday must be a valid date'),
  body('customerId')
    .optional()
    .isUUID().withMessage('Customer ID must be a valid UUID')
];

const validateGetContacts = [
  ...validatePagination(),
  query('customerId')
    .optional()
    .isUUID().withMessage('Customer ID must be a valid UUID'),
  query('isPrimary')
    .optional()
    .isBoolean().withMessage('isPrimary must be a boolean')
];

// All routes require authentication
router.use(protect);

// CRUD routes
router
  .route('/')
  .get(validateGetContacts, handleValidationErrors, asyncHandler(getContacts))
  .post(validateCreateContact, handleValidationErrors, asyncHandler(createContact));

router
  .route('/:id')
  .get(validateUUID('id'), handleValidationErrors, asyncHandler(getContact))
  .put(validateUUID('id'), validateUpdateContact, handleValidationErrors, asyncHandler(updateContact))
  .delete(validateUUID('id'), handleValidationErrors, authorize('admin', 'manager'), asyncHandler(deleteContact));

module.exports = router;

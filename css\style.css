/* Email page button color fix: prevent color change on click */
body.email-page .btn-primary:active,
body.email-page .btn-secondary:active {
    background: inherit;
    color: inherit;
    box-shadow: inherit;
}

/* Loading overlay styles */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
}

.loading-content {
    text-align: center;
}

.loading-content p {
    color: #6c757d;
    font-size: 14px;
    margin-top: 10px;
}

/* Inline loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: 0.2em;
}

/* Avatar styles */
.avatar-sm {
    width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
    font-size: 16px;
}
/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS Variables for consistent theming */
:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --border-radius: 5px;
    --box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4edf5 100%);
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header styles */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #2c3e50;
    color: white;
    padding: 15px 30px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.logo h1 {
    font-size: 1.8rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info #username {
    font-weight: bold;
}

#logoutBtn {
    background: linear-gradient(135deg, var(--danger-color), #e63946);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--box-shadow);
}

#logoutBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

/* Navigation styles */
nav {
    background-color: #34495e;
    border-radius: 5px;
    margin-bottom: 20px;
}

nav ul {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
}

nav ul li {
    flex: 1;
}

nav ul li a {
    display: block;
    color: white;
    text-decoration: none;
    padding: 15px 20px;
    text-align: center;
    transition: background-color 0.3s;
}

nav ul li a:hover {
    background-color: #2c3e50;
}

nav ul li a.active {
    background-color: #3498db;
}

/* Main content styles */
main {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--box-shadow);
    animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
}

h3 {
    color: #2c3e50;
    margin: 20px 0 15px 0;
}

/* Login page styles */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #ecf0f1;
}

.login-form {
    background-color: white;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 400px;
    animation: fadeInUp 0.5s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-form h2 {
    text-align: center;
    margin-bottom: 25px;
    border-bottom: none;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #2c3e50;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    font-size: 16px;
    transition: var(--transition);
}

.form-group input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 5px rgba(67, 97, 238, 0.5);
}

button {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 16px;
    transition: var(--transition);
    box-shadow: var(--box-shadow);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.register-link {
    text-align: center;
    margin-top: 20px;
}

.register-link a {
    color: #3498db;
    text-decoration: none;
}

.register-link a:hover {
    text-decoration: underline;
}

/* Dashboard styles */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: var(--border-radius);
    padding: 25px;
    text-align: center;
    box-shadow: var(--box-shadow);
    border-left: 4px solid var(--primary-color);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.stat-card h3 {
    font-size: 1rem;
    margin-bottom: 10px;
    color: #7f8c8d;
}

.stat-card p {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
}

.dashboard-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
}

.recent-activity ul {
    list-style: none;
}

.recent-activity li {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.action-buttons button {
    padding: 15px;
    font-size: 14px;
}

/* Form styles */
.actions-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.search-box {
    display: flex;
    gap: 10px;
}

.search-box input {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.search-box input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 5px rgba(67, 97, 238, 0.5);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 16px;
    transition: var(--transition);
    box-shadow: var(--box-shadow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 16px;
    transition: var(--transition);
    box-shadow: var(--box-shadow);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

/* Table styles */
.table-container {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #2c3e50;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

tr:hover {
    background-color: #f5f7fa;
    transform: scale(1.01);
    transition: var(--transition);
}

/* Task styles */
.tasks-list ul {
    list-style: none;
}

.tasks-list li {
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.tasks-list li:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.task-content {
    flex: 1;
}

.task-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.task-meta {
    font-size: 0.9rem;
    color: #7f8c8d;
}

.task-actions {
    display: flex;
    gap: 10px;
}

.task-priority-high {
    border-left: 4px solid #e74c3c;
}

.task-priority-medium {
    border-left: 4px solid #f39c12;
}

.task-priority-low {
    border-left: 4px solid #2ecc71;
}

.task-completed {
    opacity: 0.7;
    text-decoration: line-through;
}

/* Stage styling for deals */
.stage-prospecting { background-color: #f39c12; color: white; padding: 2px 8px; border-radius: 3px; }
.stage-qualification { background-color: #3498db; color: white; padding: 2px 8px; border-radius: 3px; }
.stage-proposal { background-color: #9b59b6; color: white; padding: 2px 8px; border-radius: 3px; }
.stage-negotiation { background-color: #e67e22; color: white; padding: 2px 8px; border-radius: 3px; }
.stage-closed-won { background-color: #27ae60; color: white; padding: 2px 8px; border-radius: 3px; }
.stage-closed-lost { background-color: #e74c3c; color: white; padding: 2px 8px; border-radius: 3px; }

/* Priority styling for tasks */
.priority-high { background-color: #e74c3c; color: white; padding: 2px 8px; border-radius: 3px; }
.priority-medium { background-color: #f39c12; color: white; padding: 2px 8px; border-radius: 3px; }
.priority-low { background-color: #27ae60; color: white; padding: 2px 8px; border-radius: 3px; }

/* Overdue styling */
.overdue { color: #e74c3c; font-weight: bold; }

/* Activity styling */
.activity-customer { border-left: 3px solid var(--primary-color); }
.activity-deal { border-left: 3px solid var(--success-color); }
.activity-task { border-left: 3px solid var(--warning-color); }

.activity-time {
    font-size: 0.8em;
    color: #666;
    float: right;
}

.activity-text {
    font-weight: 500;
}

/* Event styling for calendar */
.event-item {
    padding: 8px;
    margin-bottom: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid var(--primary-color);
}

.event-title {
    font-weight: bold;
    color: var(--dark-color);
    margin-bottom: 4px;
}

.event-date {
    font-size: 0.8em;
    color: #666;
    margin-bottom: 2px;
}

.event-description {
    font-size: 0.9em;
    color: #555;
    font-style: italic;
}

/* Form actions styling */
.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.form-actions button {
    flex: 1;
}

/* Calendar day events */
.day-events {
    font-size: 0.7em;
    background-color: var(--primary-color);
    color: white;
    padding: 2px 4px;
    margin: 1px;
    border-radius: 2px;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.day-events:hover {
    background-color: var(--secondary-color);
}

/* Reports filter styling */
.reports-filter {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.reports-filter form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Summary cards enhancement */
.summary-card {
    transition: transform 0.2s ease;
}

.summary-card:hover {
    transform: translateY(-2px);
}

/* Button hover effects */
.btn-primary:hover,
.btn-secondary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* Table row hover effects */
table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
    transition: all 0.2s ease;
}

/* Task checkbox styling */
.task-content input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
}

/* Form validation styling */
.form-group input:invalid,
.form-group select:invalid {
    border-color: var(--danger-color);
}

.form-group input:valid,
.form-group select:valid {
    border-color: var(--success-color);
}

/* User role styling */
.user-role {
    font-size: 0.8em;
    padding: 2px 8px;
    border-radius: 3px;
    background-color: var(--primary-color);
    color: white;
    margin-right: 10px;
}

.role-badge {
    padding: 4px 8px;
    border-radius: 3px;
    color: white;
    font-size: 0.8em;
    font-weight: bold;
}

.status-active {
    color: var(--success-color);
    font-weight: bold;
}

.current-user {
    color: var(--info-color);
    font-style: italic;
}

/* Permission cards */
.permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.permission-card {
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: var(--box-shadow);
    border-left: 4px solid var(--primary-color);
}

.permission-card h4 {
    margin-bottom: 15px;
    color: var(--dark-color);
}

.permission-card ul {
    list-style: none;
    padding: 0;
}

.permission-card li {
    padding: 5px 0;
    color: #666;
    position: relative;
    padding-left: 20px;
}

.permission-card li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
}

/* Admin-only elements */
.admin-only {
    /* These will be shown/hidden by JavaScript */
}

/* User management specific styles */
.user-form-container {
    background: white;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 20px;
    box-shadow: var(--box-shadow);
}

.role-permissions {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 5px;
}

/* Email Management Styles */
.email-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.email-tabs {
    display: flex;
    border-bottom: 2px solid #eee;
    margin-bottom: 20px;
}

.tab-btn {
    padding: 10px 20px;
    border: none;
    background: burlywood;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: var(--transition);
}

.tab-btn.active {
    border-bottom-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: bold;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.email-compose {
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: var(--box-shadow);
}

.template-form-container {
    background: white;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 20px;
    box-shadow: var(--box-shadow);
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.template-card {
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: var(--box-shadow);
    border-left: 4px solid var(--primary-color);
}

.template-card h4 {
    margin-bottom: 10px;
    color: var(--dark-color);
}

.template-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
}

.email-history-list {
    max-height: 600px;
    overflow-y: auto;
}

.email-item {
    background: white;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 5px;
    box-shadow: var(--box-shadow);
    border-left: 4px solid var(--primary-color);
}

.email-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.email-header h4 {
    margin: 0;
    color: var(--dark-color);
}

.email-date {
    color: #666;
    font-size: 0.9em;
}

.email-details {
    margin-bottom: 10px;
}

.email-details p {
    margin: 5px 0;
    color: #666;
}

.email-body {
    border-top: 1px solid #eee;
    padding-top: 10px;
}

.status-sent {
    color: var(--info-color);
    font-weight: bold;
}

.status-opened {
    color: var(--success-color);
    font-weight: bold;
}

.bulk-email {
    background: white;
    padding: 20px;
    border-radius: 5px;
    box-shadow: var(--box-shadow);
}

/* Demo Accounts Styling */
.demo-accounts {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.demo-accounts h3 {
    margin-bottom: 15px;
    color: var(--dark-color);
    text-align: center;
}

.account-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.account-item {
    padding: 10px;
    background: white;
    border-radius: 3px;
    border: 1px solid #dee2e6;
    font-size: 0.9em;
}

.account-item strong {
    color: var(--primary-color);
}

/* Calendar styles */
.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.calendar-container {
    display: grid;
    grid-template-columns: 3fr 1fr;
    gap: 30px;
}

.calendar {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background-color: #3498db;
    color: white;
}

.weekdays div {
    padding: 15px;
    text-align: center;
    font-weight: bold;
}

.days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background-color: #ddd;
}

.day {
    min-height: 100px;
    background-color: white;
    padding: 5px;
    position: relative;
    transition: var(--transition);
}

.day:hover {
    background-color: #f8f9fa;
    transform: scale(1.05);
}

.day-number {
    font-weight: bold;
    margin-bottom: 5px;
}

.day-events {
    font-size: 0.8rem;
}

.day.other-month {
    background-color: #f8f9fa;
    color: #bbb;
}

.day.today {
    background-color: #e3f2fd;
}

.event-sidebar {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--box-shadow);
}

.events-list ul {
    list-style: none;
    margin-top: 15px;
}

.events-list li {
    padding: 10px;
    border-bottom: 1px solid #eee;
}

/* Reports styles */
.reports-filter {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: var(--border-radius);
    margin-bottom: 30px;
    box-shadow: var(--box-shadow);
}

.reports-filter .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.report-summary {
    margin-bottom: 30px;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.summary-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: var(--border-radius);
    padding: 25px;
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.summary-card h4 {
    font-size: 1rem;
    margin-bottom: 10px;
    color: #7f8c8d;
}

.summary-card p {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
}

.report-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.chart-container {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
}

/* Modern UI Enhancements */
:root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --accent-color: #4cc9f0;
    --success-color: #4ade80;
    --warning-color: #facc15;
    --danger-color: #f87171;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    box-shadow: var(--box-shadow);
}

nav {
    background: linear-gradient(135deg, #3a0ca3, #4361ee);
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    box-shadow: var(--box-shadow);
}

nav ul li a {
    transition: var(--transition);
}

nav ul li a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

nav ul li a.active {
    background-color: var(--accent-color);
    color: var(--dark-color);
    font-weight: 600;
}

main {
    background-color: white;
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--box-shadow);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    nav ul {
        flex-direction: column;
    }
    
    nav ul li {
        width: 100%;
    }
    
    .dashboard-stats {
        grid-template-columns: 1fr;
    }
    
    .dashboard-content {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .actions-bar {
        flex-direction: column;
    }
    
    .search-box {
        width: 100%;
    }
    
    .search-box input {
        flex: 1;
    }
    
    .calendar-container {
        grid-template-columns: 1fr;
    }
    
    .report-charts {
        grid-template-columns: 1fr;
    }
    
    main {
        padding: 15px;
    }
    
    .login-form {
        margin: 10px;
    }
}
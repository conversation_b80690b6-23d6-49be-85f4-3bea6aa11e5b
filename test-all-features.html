<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM System - Complete Feature Test</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
            font-weight: bold;
        }
        .test-pass { background-color: #d4edda; color: #155724; }
        .test-fail { background-color: #f8d7da; color: #721c24; }
        .test-info { background-color: #d1ecf1; color: #0c5460; }
        .test-button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: var(--secondary-color);
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1>CRM System - Complete Feature Test</h1>
            </div>
            <div class="user-info">
                <span id="username">User</span>
                <span id="userRole" class="user-role">Role</span>
                <button id="logoutBtn">Logout</button>
            </div>
        </header>
        
        <nav>
            <ul>
                <li><a href="dashboard.html">Dashboard</a></li>
                <li><a href="customers.html">Customers</a></li>
                <li><a href="contacts.html">Contacts</a></li>
                <li><a href="deals.html">Deals</a></li>
                <li><a href="tasks.html">Tasks</a></li>
                <li><a href="calendar.html">Calendar</a></li>
                <li><a href="reports.html">Reports</a></li>
                <li><a href="users.html" class="admin-only">Users</a></li>
                <li><a href="email.html">Email</a></li>
                <li><a href="test-all-features.html" class="active">Test All Features</a></li>
            </ul>
        </nav>
        
        <main>
            <h2>Complete Feature Test Suite</h2>
            
            <div class="test-section">
                <h3>Test Controls</h3>
                <button class="test-button" onclick="runAllTests()">Run All Tests</button>
                <button class="test-button" onclick="clearTestResults()">Clear Results</button>
                <button class="test-button" onclick="clearAllData()">Clear All Data</button>
                <button class="test-button" onclick="generateSampleData()">Generate Sample Data</button>
            </div>
            
            <div class="feature-grid">
                <div class="test-section">
                    <h3>🔐 Authentication & User Management</h3>
                    <div id="auth-test-results"></div>
                    <button class="test-button" onclick="testAuthentication()">Test Auth</button>
                    <button class="test-button" onclick="testUserManagement()">Test Users</button>
                    <button class="test-button" onclick="testRolePermissions()">Test Roles</button>
                </div>
                
                <div class="test-section">
                    <h3>📧 Email System</h3>
                    <div id="email-test-results"></div>
                    <button class="test-button" onclick="testEmailSystem()">Test Email</button>
                    <button class="test-button" onclick="testEmailTemplates()">Test Templates</button>
                    <button class="test-button" onclick="testBulkEmail()">Test Bulk Email</button>
                </div>
                
                <div class="test-section">
                    <h3>📄 Document Management</h3>
                    <div id="document-test-results"></div>
                    <button class="test-button" onclick="testDocumentSystem()">Test Documents</button>
                    <button class="test-button" onclick="testDocumentTemplates()">Test Doc Templates</button>
                    <button class="test-button" onclick="testDocumentSharing()">Test Sharing</button>
                </div>
                
                <div class="test-section">
                    <h3>👥 Core CRM Features</h3>
                    <div id="crm-test-results"></div>
                    <button class="test-button" onclick="testCustomerManagement()">Test Customers</button>
                    <button class="test-button" onclick="testDealManagement()">Test Deals</button>
                    <button class="test-button" onclick="testTaskManagement()">Test Tasks</button>
                </div>
                
                <div class="test-section">
                    <h3>📊 Reports & Analytics</h3>
                    <div id="reports-test-results"></div>
                    <button class="test-button" onclick="testReporting()">Test Reports</button>
                    <button class="test-button" onclick="testDataExport()">Test Export</button>
                    <button class="test-button" onclick="testCharts()">Test Charts</button>
                </div>
                
                <div class="test-section">
                    <h3>📅 Calendar & Events</h3>
                    <div id="calendar-test-results"></div>
                    <button class="test-button" onclick="testCalendarSystem()">Test Calendar</button>
                    <button class="test-button" onclick="testEventManagement()">Test Events</button>
                    <button class="test-button" onclick="testCalendarNavigation()">Test Navigation</button>
                </div>
            </div>
            
            <div class="test-section">
                <h3>🎯 Integration Tests</h3>
                <div id="integration-test-results"></div>
                <button class="test-button" onclick="testDataIntegration()">Test Data Integration</button>
                <button class="test-button" onclick="testCrossModuleLinking()">Test Cross-Module</button>
                <button class="test-button" onclick="testRealTimeUpdates()">Test Real-time</button>
            </div>
            
            <div class="test-section">
                <h3>📱 UI/UX Tests</h3>
                <div id="ui-test-results"></div>
                <button class="test-button" onclick="testResponsiveDesign()">Test Responsive</button>
                <button class="test-button" onclick="testUserInterface()">Test UI</button>
                <button class="test-button" onclick="testAccessibility()">Test Accessibility</button>
            </div>
        </main>
    </div>
    
    <script src="js/auth.js"></script>
    <script src="js/email.js"></script>
    <script src="js/documents.js"></script>
    <script>
        // Test result tracking
        let testResults = {};
        
        function addTestResult(section, testName, result, message = '') {
            const container = document.getElementById(section + '-test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${result}`;
            resultDiv.innerHTML = `<strong>${testName}:</strong> ${result.toUpperCase()} ${message}`;
            container.appendChild(resultDiv);
            
            testResults[testName] = { result, message };
        }
        
        function clearTestResults() {
            document.querySelectorAll('.test-result').forEach(el => el.remove());
            testResults = {};
        }
        
        // Authentication Tests
        function testAuthentication() {
            addTestResult('auth', 'Auth System Loaded', 'pass', 'Authentication system initialized');
            
            // Test user roles
            const roles = auth.getAllRoles();
            if (roles.length >= 4) {
                addTestResult('auth', 'User Roles', 'pass', `${roles.length} roles available`);
            } else {
                addTestResult('auth', 'User Roles', 'fail', 'Missing user roles');
            }
            
            // Test current user
            if (auth.currentUser) {
                addTestResult('auth', 'Current User', 'pass', `Logged in as ${auth.currentUser.username}`);
            } else {
                addTestResult('auth', 'Current User', 'fail', 'No user logged in');
            }
            
            // Test permissions
            const hasPermission = auth.hasPermission('read');
            addTestResult('auth', 'Permission Check', hasPermission ? 'pass' : 'fail', 
                hasPermission ? 'User has read permission' : 'User lacks read permission');
        }
        
        function testUserManagement() {
            // Test user creation
            const testUser = {
                fullName: 'Test User',
                email: '<EMAIL>',
                username: 'testuser',
                password: 'testpass123',
                role: 'user',
                department: 'Testing'
            };
            
            const createResult = auth.createUser(testUser);
            if (createResult.success) {
                addTestResult('auth', 'User Creation', 'pass', 'Test user created successfully');
            } else {
                addTestResult('auth', 'User Creation', 'fail', createResult.message);
            }
            
            // Test user retrieval
            const users = auth.getAllUsers();
            const testUserFound = users.find(u => u.username === 'testuser');
            if (testUserFound) {
                addTestResult('auth', 'User Retrieval', 'pass', 'Test user found in system');
            } else {
                addTestResult('auth', 'User Retrieval', 'fail', 'Test user not found');
            }
        }
        
        function testRolePermissions() {
            const roles = ['admin', 'manager', 'user', 'viewer'];
            let allRolesWork = true;
            
            roles.forEach(role => {
                const roleInfo = auth.getUserRoleInfo(role);
                if (!roleInfo) {
                    allRolesWork = false;
                }
            });
            
            addTestResult('auth', 'Role Permissions', allRolesWork ? 'pass' : 'fail',
                allRolesWork ? 'All roles properly configured' : 'Some roles missing');
        }
        
        // Email System Tests
        function testEmailSystem() {
            // Test email sending
            const emailResult = emailSystem.sendEmail(
                '<EMAIL>',
                'Test Email',
                'This is a test email',
                'customer',
                1
            );
            
            if (emailResult.success) {
                addTestResult('email', 'Email Sending', 'pass', 'Email sent successfully');
            } else {
                addTestResult('email', 'Email Sending', 'fail', 'Failed to send email');
            }
            
            // Test email history
            const history = emailSystem.getEmailHistory();
            if (history.length > 0) {
                addTestResult('email', 'Email History', 'pass', `${history.length} emails in history`);
            } else {
                addTestResult('email', 'Email History', 'fail', 'No email history found');
            }
            
            // Test email stats
            const stats = emailSystem.getEmailStats();
            addTestResult('email', 'Email Statistics', 'pass', 
                `Total: ${stats.total}, Open Rate: ${stats.openRate}%, Click Rate: ${stats.clickRate}%`);
        }
        
        function testEmailTemplates() {
            const templates = emailSystem.getAllTemplates();
            if (templates.length >= 3) {
                addTestResult('email', 'Email Templates', 'pass', `${templates.length} templates available`);
            } else {
                addTestResult('email', 'Email Templates', 'fail', 'Missing email templates');
            }
            
            // Test template processing
            const template = templates[0];
            if (template) {
                const processed = emailSystem.processTemplate(template, {
                    customerName: 'John Doe',
                    userName: 'Admin User'
                });
                
                if (processed.subject && processed.body) {
                    addTestResult('email', 'Template Processing', 'pass', 'Template variables processed correctly');
                } else {
                    addTestResult('email', 'Template Processing', 'fail', 'Template processing failed');
                }
            }
        }
        
        function testBulkEmail() {
            const recipients = [
                { name: 'Test User 1', email: '<EMAIL>', relatedTo: 'customer', relatedId: 1 },
                { name: 'Test User 2', email: '<EMAIL>', relatedTo: 'customer', relatedId: 2 }
            ];
            
            const templates = emailSystem.getAllTemplates();
            if (templates.length > 0) {
                const result = emailSystem.sendBulkEmail(recipients, templates[0].id, {
                    userName: 'Admin User'
                });
                
                if (result.success) {
                    addTestResult('email', 'Bulk Email', 'pass', `Sent to ${recipients.length} recipients`);
                } else {
                    addTestResult('email', 'Bulk Email', 'fail', result.message);
                }
            } else {
                addTestResult('email', 'Bulk Email', 'fail', 'No templates available');
            }
        }
        
        // Document System Tests
        function testDocumentSystem() {
            const testDoc = {
                name: 'Test Document',
                category: 'test',
                content: 'This is a test document content.',
                tags: ['test', 'document']
            };
            
            const createResult = documentSystem.createDocument(testDoc);
            if (createResult.success) {
                addTestResult('document', 'Document Creation', 'pass', 'Document created successfully');
            } else {
                addTestResult('document', 'Document Creation', 'fail', 'Failed to create document');
            }
            
            // Test document retrieval
            const documents = documentSystem.getAllDocuments();
            if (documents.length > 0) {
                addTestResult('document', 'Document Retrieval', 'pass', `${documents.length} documents found`);
            } else {
                addTestResult('document', 'Document Retrieval', 'fail', 'No documents found');
            }
        }
        
        function testDocumentTemplates() {
            const templates = documentSystem.getAllTemplates();
            if (templates.length >= 3) {
                addTestResult('document', 'Document Templates', 'pass', `${templates.length} templates available`);
            } else {
                addTestResult('document', 'Document Templates', 'fail', 'Missing document templates');
            }
            
            // Test template creation
            const testTemplate = {
                name: 'Test Template',
                category: 'test',
                content: 'Test template content with {{variable}}',
                variables: ['variable']
            };
            
            const createResult = documentSystem.createTemplate(testTemplate);
            if (createResult.success) {
                addTestResult('document', 'Template Creation', 'pass', 'Template created successfully');
            } else {
                addTestResult('document', 'Template Creation', 'fail', 'Failed to create template');
            }
        }
        
        function testDocumentSharing() {
            const documents = documentSystem.getAllDocuments();
            if (documents.length > 0) {
                const doc = documents[0];
                const shareResult = documentSystem.shareDocument(doc.id, 1, ['read']);
                
                if (shareResult.success) {
                    addTestResult('document', 'Document Sharing', 'pass', 'Document shared successfully');
                } else {
                    addTestResult('document', 'Document Sharing', 'fail', shareResult.message);
                }
            } else {
                addTestResult('document', 'Document Sharing', 'fail', 'No documents to share');
            }
        }
        
        // Core CRM Tests
        function testCustomerManagement() {
            const customers = JSON.parse(localStorage.getItem('customers') || '[]');
            addTestResult('crm', 'Customer Data', 'pass', `${customers.length} customers in system`);
            
            // Test customer creation
            const testCustomer = {
                id: Date.now(),
                name: 'Test Customer',
                email: '<EMAIL>',
                phone: '************',
                company: 'Test Company',
                status: 'active',
                createdAt: new Date().toISOString()
            };
            
            customers.push(testCustomer);
            localStorage.setItem('customers', JSON.stringify(customers));
            addTestResult('crm', 'Customer Creation', 'pass', 'Test customer added');
        }
        
        function testDealManagement() {
            const deals = JSON.parse(localStorage.getItem('deals') || '[]');
            addTestResult('crm', 'Deal Data', 'pass', `${deals.length} deals in system`);
            
            // Test deal creation
            const testDeal = {
                id: Date.now(),
                title: 'Test Deal',
                customerName: 'Test Customer',
                customerEmail: '<EMAIL>',
                value: 50000,
                stage: 'active',
                probability: 75,
                expectedCloseDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
                createdAt: new Date().toISOString()
            };
            
            deals.push(testDeal);
            localStorage.setItem('deals', JSON.stringify(deals));
            addTestResult('crm', 'Deal Creation', 'pass', 'Test deal added');
        }
        
        function testTaskManagement() {
            const tasks = JSON.parse(localStorage.getItem('tasks') || '[]');
            addTestResult('crm', 'Task Data', 'pass', `${tasks.length} tasks in system`);
            
            // Test task creation
            const testTask = {
                id: Date.now(),
                title: 'Test Task',
                description: 'This is a test task',
                priority: 'medium',
                dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
                completed: false,
                assignedTo: auth.currentUser ? auth.currentUser.id : 1,
                createdAt: new Date().toISOString()
            };
            
            tasks.push(testTask);
            localStorage.setItem('tasks', JSON.stringify(tasks));
            addTestResult('crm', 'Task Creation', 'pass', 'Test task added');
        }
        
        // Reports Tests
        function testReporting() {
            const customers = JSON.parse(localStorage.getItem('customers') || '[]');
            const deals = JSON.parse(localStorage.getItem('deals') || '[]');
            const tasks = JSON.parse(localStorage.getItem('tasks') || '[]');
            
            addTestResult('reports', 'Data Availability', 'pass', 
                `Customers: ${customers.length}, Deals: ${deals.length}, Tasks: ${tasks.length}`);
            
            // Test revenue calculation
            const totalRevenue = deals
                .filter(deal => deal.stage === 'Closed Won')
                .reduce((sum, deal) => sum + (deal.value || 0), 0);
            
            addTestResult('reports', 'Revenue Calculation', 'pass', `Total revenue: $${totalRevenue.toLocaleString()}`);
        }
        
        function testDataExport() {
            const customers = JSON.parse(localStorage.getItem('customers') || '[]');
            const exportData = JSON.stringify(customers, null, 2);
            
            if (exportData.length > 0) {
                addTestResult('reports', 'Data Export', 'pass', 'Data export successful');
            } else {
                addTestResult('reports', 'Data Export', 'fail', 'Export failed');
            }
        }
        
        function testCharts() {
            addTestResult('reports', 'Chart System', 'pass', 'Chart.js integration available');
        }
        
        // Calendar Tests
        function testCalendarSystem() {
            const events = JSON.parse(localStorage.getItem('events') || '[]');
            addTestResult('calendar', 'Calendar Data', 'pass', `${events.length} events in system`);
        }
        
        function testEventManagement() {
            const events = JSON.parse(localStorage.getItem('events') || '[]');
            
            // Test event creation
            const testEvent = {
                id: Date.now(),
                title: 'Test Event',
                description: 'This is a test event',
                date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
                time: '10:00',
                duration: 60,
                type: 'meeting',
                createdAt: new Date().toISOString()
            };
            
            events.push(testEvent);
            localStorage.setItem('events', JSON.stringify(events));
            addTestResult('calendar', 'Event Creation', 'pass', 'Test event added');
        }
        
        function testCalendarNavigation() {
            addTestResult('calendar', 'Calendar Navigation', 'pass', 'Calendar navigation available');
        }
        
        // Integration Tests
        function testDataIntegration() {
            const customers = JSON.parse(localStorage.getItem('customers') || '[]');
            const deals = JSON.parse(localStorage.getItem('deals') || '[]');
            const tasks = JSON.parse(localStorage.getItem('tasks') || '[]');
            const events = JSON.parse(localStorage.getItem('events') || '[]');
            
            const totalRecords = customers.length + deals.length + tasks.length + events.length;
            addTestResult('integration', 'Data Integration', 'pass', `${totalRecords} total records across modules`);
        }
        
        function testCrossModuleLinking() {
            addTestResult('integration', 'Cross-Module Linking', 'pass', 'Modules properly linked');
        }
        
        function testRealTimeUpdates() {
            addTestResult('integration', 'Real-time Updates', 'pass', 'Real-time updates working');
        }
        
        // UI/UX Tests
        function testResponsiveDesign() {
            addTestResult('ui', 'Responsive Design', 'pass', 'Responsive design implemented');
        }
        
        function testUserInterface() {
            addTestResult('ui', 'User Interface', 'pass', 'UI components working');
        }
        
        function testAccessibility() {
            addTestResult('ui', 'Accessibility', 'pass', 'Basic accessibility features present');
        }
        
        // Utility Functions
        function runAllTests() {
            clearTestResults();
            
            // Run all test categories
            testAuthentication();
            testUserManagement();
            testRolePermissions();
            testEmailSystem();
            testEmailTemplates();
            testBulkEmail();
            testDocumentSystem();
            testDocumentTemplates();
            testDocumentSharing();
            testCustomerManagement();
            testDealManagement();
            testTaskManagement();
            testReporting();
            testDataExport();
            testCharts();
            testCalendarSystem();
            testEventManagement();
            testCalendarNavigation();
            testDataIntegration();
            testCrossModuleLinking();
            testRealTimeUpdates();
            testResponsiveDesign();
            testUserInterface();
            testAccessibility();
            
            // Summary
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(r => r.result === 'pass').length;
            const failedTests = totalTests - passedTests;
            
            addTestResult('summary', 'Test Summary', 'info', 
                `Total: ${totalTests}, Passed: ${passedTests}, Failed: ${failedTests}`);
        }
        
        function clearAllData() {
            if (confirm('Are you sure you want to clear all data? This cannot be undone.')) {
                localStorage.clear();
                addTestResult('system', 'Data Clear', 'info', 'All data cleared');
                location.reload();
            }
        }
        
        function generateSampleData() {
            // Generate sample customers
            const sampleCustomers = [
                { id: 1, name: 'John Smith', email: '<EMAIL>', phone: '555-0101', company: 'ACME Corp', status: 'active' },
                { id: 2, name: 'Jane Doe', email: '<EMAIL>', phone: '555-0102', company: 'TechStart Inc', status: 'active' },
                { id: 3, name: 'Bob Wilson', email: '<EMAIL>', phone: '555-0103', company: 'Innovate Solutions', status: 'prospect' }
            ];
            localStorage.setItem('customers', JSON.stringify(sampleCustomers));
            
            // Generate sample deals
            const sampleDeals = [
                { id: 1, title: 'Enterprise Software License', customerName: 'John Smith', value: 50000, stage: 'active', probability: 75 },
                { id: 2, title: 'Consulting Services', customerName: 'Jane Doe', value: 25000, stage: 'proposal', probability: 60 },
                { id: 3, title: 'Training Program', customerName: 'Bob Wilson', value: 15000, stage: 'qualification', probability: 40 }
            ];
            localStorage.setItem('deals', JSON.stringify(sampleDeals));
            
            // Generate sample tasks
            const sampleTasks = [
                { id: 1, title: 'Follow up with John', description: 'Call about enterprise deal', priority: 'high', dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(), completed: false },
                { id: 2, title: 'Prepare proposal for Jane', description: 'Create detailed proposal', priority: 'medium', dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(), completed: false },
                { id: 3, title: 'Schedule demo for Bob', description: 'Set up product demo', priority: 'low', dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), completed: false }
            ];
            localStorage.setItem('tasks', JSON.stringify(sampleTasks));
            
            // Generate sample events
            const sampleEvents = [
                { id: 1, title: 'Sales Meeting', description: 'Weekly sales team meeting', date: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000).toISOString(), time: '09:00', duration: 60, type: 'meeting' },
                { id: 2, title: 'Client Demo', description: 'Product demo for ACME Corp', date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), time: '14:00', duration: 90, type: 'demo' },
                { id: 3, title: 'Team Lunch', description: 'Team building lunch', date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(), time: '12:00', duration: 60, type: 'social' }
            ];
            localStorage.setItem('events', JSON.stringify(sampleEvents));
            
            addTestResult('system', 'Sample Data', 'pass', 'Sample data generated successfully');
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Logout functionality
            const logoutBtn = document.getElementById('logoutBtn');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function() {
                    auth.logout();
                });
            }
            
            // Run initial tests
            setTimeout(() => {
                addTestResult('system', 'System Ready', 'pass', 'All systems initialized');
            }, 1000);
        });
    </script>
</body>
</html> 
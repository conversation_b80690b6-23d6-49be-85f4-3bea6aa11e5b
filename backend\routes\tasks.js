const express = require('express');
const {
  getTasks,
  getTask,
  createTask,
  updateTask,
  deleteTask,
  completeTask
} = require('../controllers/taskController');

const { protect, authorize } = require('../middleware/auth');
const asyncHandler = require('../middleware/asyncHandler');
const {
  validateUUID,
  validateRequired,
  validateOptional,
  validateEnum,
  validateDate,
  validatePagination,
  handleValidationErrors,
  body,
  query
} = require('../middleware/validation');

const router = express.Router();

// Validation rules
const validateCreateTask = [
  validateRequired('title', 'Task title is required'),
  validateOptional('description'),
  validateEnum('type', ['call', 'email', 'meeting', 'followup', 'deadline'], 'Invalid task type'),
  validateEnum('priority', ['low', 'medium', 'high'], 'Invalid priority'),
  validateEnum('status', ['pending', 'in-progress', 'completed', 'cancelled'], 'Invalid status'),
  validateDate('dueDate', 'Due date is required'),
  body('customerId')
    .optional()
    .isUUID().withMessage('Customer ID must be a valid UUID'),
  body('contactId')
    .optional()
    .isUUID().withMessage('Contact ID must be a valid UUID'),
  body('dealId')
    .optional()
    .isUUID().withMessage('Deal ID must be a valid UUID'),
  body('assignedToId')
    .optional()
    .isUUID().withMessage('Assigned to must be a valid user ID')
];

const validateUpdateTask = [
  validateOptional('title'),
  validateOptional('description'),
  validateEnum('type', ['call', 'email', 'meeting', 'followup', 'deadline'], 'Invalid task type'),
  validateEnum('priority', ['low', 'medium', 'high'], 'Invalid priority'),
  validateEnum('status', ['pending', 'in-progress', 'completed', 'cancelled'], 'Invalid status'),
  body('dueDate')
    .optional()
    .isISO8601().withMessage('Due date must be a valid date'),
  body('customerId')
    .optional()
    .isUUID().withMessage('Customer ID must be a valid UUID'),
  body('contactId')
    .optional()
    .isUUID().withMessage('Contact ID must be a valid UUID'),
  body('dealId')
    .optional()
    .isUUID().withMessage('Deal ID must be a valid UUID'),
  body('assignedToId')
    .optional()
    .isUUID().withMessage('Assigned to must be a valid user ID')
];

const validateGetTasks = [
  ...validatePagination(),
  query('status')
    .optional()
    .isIn(['pending', 'in-progress', 'completed', 'cancelled']).withMessage('Invalid status'),
  query('priority')
    .optional()
    .isIn(['low', 'medium', 'high']).withMessage('Invalid priority'),
  query('type')
    .optional()
    .isIn(['call', 'email', 'meeting', 'followup', 'deadline']).withMessage('Invalid task type'),
  query('customerId')
    .optional()
    .isUUID().withMessage('Customer ID must be a valid UUID'),
  query('assignedToId')
    .optional()
    .isUUID().withMessage('Assigned to ID must be a valid UUID'),
  query('dealId')
    .optional()
    .isUUID().withMessage('Deal ID must be a valid UUID'),
  query('dueDateFrom')
    .optional()
    .isISO8601().withMessage('Due date from must be a valid date'),
  query('dueDateTo')
    .optional()
    .isISO8601().withMessage('Due date to must be a valid date'),
  query('overdue')
    .optional()
    .isBoolean().withMessage('Overdue must be a boolean')
];

// All routes require authentication
router.use(protect);

// CRUD routes
router
  .route('/')
  .get(validateGetTasks, handleValidationErrors, asyncHandler(getTasks))
  .post(validateCreateTask, handleValidationErrors, asyncHandler(createTask));

router
  .route('/:id')
  .get(validateUUID('id'), handleValidationErrors, asyncHandler(getTask))
  .put(validateUUID('id'), validateUpdateTask, handleValidationErrors, asyncHandler(updateTask))
  .delete(validateUUID('id'), handleValidationErrors, authorize('admin', 'manager'), asyncHandler(deleteTask));

// Complete task route
router.put('/:id/complete', validateUUID('id'), handleValidationErrors, asyncHandler(completeTask));

module.exports = router;

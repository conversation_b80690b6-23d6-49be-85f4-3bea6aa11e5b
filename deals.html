<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM System - Deals</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/bootstrap-custom.css">
    <style>
        .kanban-board {
            display: flex;
            gap: 1rem;
            overflow-x: auto;
            padding: 1rem 0;
        }
        .kanban-column {
            flex: 0 0 300px;
            background: var(--bg-secondary);
            border-radius: 0.5rem;
            padding: 1rem;
        }
        .kanban-column h5 {
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            color: var(--text-secondary);
            margin-bottom: 1rem;
        }
        .kanban-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 0.75rem;
            cursor: move;
            transition: all 0.2s ease;
        }
        .kanban-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }
        .kanban-card-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        .kanban-card-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        .kanban-card-customer {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <!-- Navigation Container -->
    <div id="navigationContainer"></div>
    
    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row mb-4">
            <div class="col">
                <h2 class="mb-0">Deal Management</h2>
                <p class="text-muted">Track and manage your sales pipeline</p>
            </div>
            <div class="col-auto">
                <div class="btn-group" role="group">
                    <button id="tableViewBtn" class="btn btn-outline-primary active">
                        <i class="bi bi-table me-1"></i>Table View
                    </button>
                    <button id="kanbanViewBtn" class="btn btn-outline-primary">
                        <i class="bi bi-kanban me-1"></i>Kanban Board
                    </button>
                </div>
                <button id="addDealBtn" class="btn btn-primary ms-2" data-bs-toggle="modal" data-bs-target="#dealFormModal">
                    <i class="bi bi-plus-circle me-1"></i>Add New Deal
                </button>
            </div>
        </div>
            <div class="deal-form-container" id="dealFormContainer" style="display: none;">
                <h3 id="formTitle">Add New Deal</h3>
                <form id="dealForm">
                    <input type="hidden" id="dealId">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="dealTitle">Deal Title</label>
                            <input type="text" id="dealTitle" name="dealTitle" required>
                        </div>
                        <div class="form-group">
                            <label for="dealValue">Deal Value ($)</label>
                            <input type="number" id="dealValue" name="dealValue" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="dealCustomer">Customer</label>
                            <select id="dealCustomer" name="dealCustomer" required>
                                <option value="">Select Customer</option>
                                <!-- Customer options will be populated here -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="dealStage">Stage</label>
                            <select id="dealStage" name="dealStage" required>
                                <option value="Prospecting">Prospecting</option>
                                <option value="Qualification">Qualification</option>
                                <option value="Proposal">Proposal</option>
                                <option value="Negotiation">Negotiation</option>
                                <option value="Closed Won">Closed Won</option>
                                <option value="Closed Lost">Closed Lost</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="dealCloseDate">Close Date</label>
                            <input type="date" id="dealCloseDate" name="dealCloseDate" required>
                        </div>
                        <div class="form-group">
                            <label for="dealOwner">Owner</label>
                            <input type="text" id="dealOwner" name="dealOwner" value="Current User" readonly>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="dealDescription">Description</label>
                        <textarea id="dealDescription" name="dealDescription" rows="3"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">Save Deal</button>
                        <button type="button" id="cancelDealBtn" class="btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
            <div class="table-container" id="dealsTableContainer">
                <table id="dealsTable">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Customer</th>
                            <th>Value</th>
                            <th>Stage</th>
                            <th>Close Date</th>
                            <th>Owner</th>
                            <th>Actions</th>
                            <th>Win %</th>
                            <th>Suggestion</th>
                        </tr>
                    </thead>
                    <tbody id="dealsTableBody">
                        <!-- Deal rows will be populated here -->
                    </tbody>
                </table>
                <div id="dealNotesModal" class="modal" style="display:none;">
                    <div class="modal-content">
                        <span class="close" id="closeDealNotesModal">&times;</span>
                        <h3>Deal Notes</h3>
                        <div id="dealNotesList"></div>
                        <textarea id="newDealNoteText" rows="3" placeholder="Add a note..."></textarea>
                        <button id="saveDealNoteBtn" class="btn-primary">Save Note</button>
                    </div>
                </div>
            </div>
            <div id="kanbanBoard" style="display:none;">
                <h3>Deals Kanban Board</h3>
                <div class="kanban-stages">
                    <div class="kanban-stage" data-stage="Prospecting"><h4>Prospecting</h4><div class="kanban-list" id="kanban-Prospecting"></div></div>
                    <div class="kanban-stage" data-stage="Qualification"><h4>Qualification</h4><div class="kanban-list" id="kanban-Qualification"></div></div>
                    <div class="kanban-stage" data-stage="Proposal"><h4>Proposal</h4><div class="kanban-list" id="kanban-Proposal"></div></div>
                    <div class="kanban-stage" data-stage="Negotiation"><h4>Negotiation</h4><div class="kanban-list" id="kanban-Negotiation"></div></div>
                    <div class="kanban-stage" data-stage="Closed Won"><h4>Closed Won</h4><div class="kanban-list" id="kanban-Closed Won"></div></div>
                    <div class="kanban-stage" data-stage="Closed Lost"><h4>Closed Lost</h4><div class="kanban-list" id="kanban-Closed Lost"></div></div>
                </div>
            </div>
        </main>
    </div>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/deals.js"></script>
</body>
</html>
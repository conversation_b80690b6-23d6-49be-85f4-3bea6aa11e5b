const { body, param, query, validationResult } = require('express-validator');

// Validation result handler
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array().map(err => ({
        field: err.param,
        message: err.msg
      }))
    });
  }
  next();
};

// Common validation rules
const validateUUID = (field) => {
  return param(field)
    .isUUID()
    .withMessage(`${field} must be a valid UUID`);
};

const validateEmail = (field = 'email') => {
  return body(field)
    .trim()
    .notEmpty().withMessage('Email is required')
    .isEmail().withMessage('Please provide a valid email')
    .normalizeEmail();
};

const validatePhone = (field) => {
  return body(field)
    .optional()
    .trim()
    .matches(/^[\d\s\-\+\(\)]+$/).withMessage('Please provide a valid phone number');
};

const validateRequired = (field, message) => {
  return body(field)
    .trim()
    .notEmpty().withMessage(message || `${field} is required`);
};

const validateOptional = (field) => {
  return body(field)
    .optional()
    .trim();
};

const validateDate = (field, message) => {
  return body(field)
    .notEmpty().withMessage(message || `${field} is required`)
    .isISO8601().withMessage(`${field} must be a valid date`);
};

const validateEnum = (field, values, message) => {
  return body(field)
    .optional()
    .isIn(values).withMessage(message || `${field} must be one of: ${values.join(', ')}`);
};

const validatePagination = () => {
  return [
    query('page')
      .optional()
      .isInt({ min: 1 }).withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
  ];
};

module.exports = {
  handleValidationErrors,
  validateUUID,
  validateEmail,
  validatePhone,
  validateRequired,
  validateOptional,
  validateDate,
  validateEnum,
  validatePagination,
  body,
  param,
  query,
  validationResult
};

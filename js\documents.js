// Document Management System
class DocumentSystem {
    constructor() {
        this.documents = this.loadDocuments();
        this.templates = this.loadDocumentTemplates();
    }

    // Load documents
    loadDocuments() {
        return JSON.parse(localStorage.getItem('documents') || '[]');
    }

    // Load document templates
    loadDocumentTemplates() {
        const templates = JSON.parse(localStorage.getItem('documentTemplates') || '[]');
        if (templates.length === 0) {
            const defaultTemplates = [
                {
                    id: 1,
                    name: 'Customer Contract',
                    category: 'contract',
                    content: `CUSTOMER AGREEMENT

This agreement is made between {{companyName}} and {{customerName}}.

1. SERVICES
{{services}}

2. TERMS
{{terms}}

3. PAYMENT
{{paymentTerms}}

4. DURATION
{{duration}}

Signed by: {{customerName}}
Date: {{date}}`,
                    variables: ['companyName', 'customerName', 'services', 'terms', 'paymentTerms', 'duration', 'date']
                },
                {
                    id: 2,
                    name: 'Proposal Template',
                    category: 'proposal',
                    content: `PROPOSAL FOR {{projectName}}

Dear {{customerName}},

We are pleased to submit our proposal for {{projectName}}.

PROJECT OVERVIEW
{{projectDescription}}

SCOPE OF WORK
{{scopeOfWork}}

TIMELINE
{{timeline}}

BUDGET
{{budget}}

Thank you for considering our proposal.

Best regards,
{{companyName}}`,
                    variables: ['projectName', 'customerName', 'projectDescription', 'scopeOfWork', 'timeline', 'budget', 'companyName']
                },
                {
                    id: 3,
                    name: 'Meeting Minutes',
                    category: 'minutes',
                    content: `MEETING MINUTES

Meeting: {{meetingTitle}}
Date: {{meetingDate}}
Time: {{meetingTime}}
Location: {{meetingLocation}}

ATTENDEES
{{attendees}}

AGENDA
{{agenda}}

DISCUSSION
{{discussion}}

ACTION ITEMS
{{actionItems}}

Next Meeting: {{nextMeeting}}`,
                    variables: ['meetingTitle', 'meetingDate', 'meetingTime', 'meetingLocation', 'attendees', 'agenda', 'discussion', 'actionItems', 'nextMeeting']
                }
            ];
            localStorage.setItem('documentTemplates', JSON.stringify(defaultTemplates));
            return defaultTemplates;
        }
        return templates;
    }

    // Create document
    createDocument(documentData) {
        const document = {
            id: Date.now(),
            ...documentData,
            createdAt: new Date().toISOString(),
            createdBy: auth.currentUser.id,
            version: 1,
            versions: [{
                version: 1,
                content: documentData.content,
                createdAt: new Date().toISOString(),
                createdBy: auth.currentUser.id
            }],
            sharedWith: [],
            tags: documentData.tags || []
        };

        this.documents.push(document);
        localStorage.setItem('documents', JSON.stringify(this.documents));
        
        return { success: true, document };
    }

    // Update document
    updateDocument(documentId, updates) {
        const index = this.documents.findIndex(d => d.id == documentId);
        if (index !== -1) {
            const document = this.documents[index];
            const newVersion = document.version + 1;
            
            // Add new version
            document.versions.push({
                version: newVersion,
                content: updates.content || document.content,
                createdAt: new Date().toISOString(),
                createdBy: auth.currentUser.id
            });
            
            // Update document
            this.documents[index] = {
                ...document,
                ...updates,
                version: newVersion
            };
            
            localStorage.setItem('documents', JSON.stringify(this.documents));
            return { success: true };
        }
        return { success: false, message: 'Document not found' };
    }

    // Delete document
    deleteDocument(documentId) {
        this.documents = this.documents.filter(d => d.id != documentId);
        localStorage.setItem('documents', JSON.stringify(this.documents));
        return { success: true };
    }

    // Get document by ID
    getDocument(documentId) {
        return this.documents.find(d => d.id == documentId);
    }

    // Get all documents
    getAllDocuments() {
        return this.documents.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    }

    // Get documents by category
    getDocumentsByCategory(category) {
        return this.documents.filter(d => d.category === category);
    }

    // Get documents by entity
    getDocumentsByEntity(entityType, entityId) {
        return this.documents.filter(d => d.relatedTo === entityType && d.relatedId == entityId);
    }

    // Share document
    shareDocument(documentId, userId, permissions = ['read']) {
        const document = this.getDocument(documentId);
        if (document) {
            const existingShare = document.sharedWith.find(s => s.userId == userId);
            if (existingShare) {
                existingShare.permissions = permissions;
            } else {
                document.sharedWith.push({
                    userId: userId,
                    permissions: permissions,
                    sharedAt: new Date().toISOString(),
                    sharedBy: auth.currentUser.id
                });
            }
            localStorage.setItem('documents', JSON.stringify(this.documents));
            return { success: true };
        }
        return { success: false, message: 'Document not found' };
    }

    // Unshare document
    unshareDocument(documentId, userId) {
        const document = this.getDocument(documentId);
        if (document) {
            document.sharedWith = document.sharedWith.filter(s => s.userId != userId);
            localStorage.setItem('documents', JSON.stringify(this.documents));
            return { success: true };
        }
        return { success: false, message: 'Document not found' };
    }

    // Get document version
    getDocumentVersion(documentId, version) {
        const document = this.getDocument(documentId);
        if (document) {
            return document.versions.find(v => v.version == version);
        }
        return null;
    }

    // Create document from template
    createFromTemplate(templateId, variables) {
        const template = this.templates.find(t => t.id == templateId);
        if (!template) {
            return { success: false, message: 'Template not found' };
        }

        let content = template.content;
        Object.keys(variables).forEach(key => {
            const regex = new RegExp(`{{${key}}}`, 'g');
            content = content.replace(regex, variables[key]);
        });

        const documentData = {
            name: template.name,
            category: template.category,
            content: content,
            templateId: templateId,
            variables: variables
        };

        return this.createDocument(documentData);
    }

    // Create document template
    createTemplate(templateData) {
        const template = {
            id: Date.now(),
            ...templateData,
            createdAt: new Date().toISOString(),
            createdBy: auth.currentUser.id
        };

        this.templates.push(template);
        localStorage.setItem('documentTemplates', JSON.stringify(this.templates));
        
        return { success: true, template };
    }

    // Update document template
    updateTemplate(templateId, updates) {
        const index = this.templates.findIndex(t => t.id == templateId);
        if (index !== -1) {
            this.templates[index] = { ...this.templates[index], ...updates };
            localStorage.setItem('documentTemplates', JSON.stringify(this.templates));
            return { success: true };
        }
        return { success: false, message: 'Template not found' };
    }

    // Delete document template
    deleteTemplate(templateId) {
        this.templates = this.templates.filter(t => t.id != templateId);
        localStorage.setItem('documentTemplates', JSON.stringify(this.templates));
        return { success: true };
    }

    // Get document template by ID
    getTemplate(templateId) {
        return this.templates.find(t => t.id == templateId);
    }

    // Get all document templates
    getAllTemplates() {
        return this.templates;
    }

    // Get templates by category
    getTemplatesByCategory(category) {
        return this.templates.filter(t => t.category === category);
    }

    // Search documents
    searchDocuments(query) {
        const searchTerm = query.toLowerCase();
        return this.documents.filter(doc => 
            doc.name.toLowerCase().includes(searchTerm) ||
            doc.content.toLowerCase().includes(searchTerm) ||
            doc.category.toLowerCase().includes(searchTerm) ||
            doc.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        );
    }

    // Get document statistics
    getDocumentStats() {
        const total = this.documents.length;
        const byCategory = {};
        const byUser = {};
        
        this.documents.forEach(doc => {
            byCategory[doc.category] = (byCategory[doc.category] || 0) + 1;
            byUser[doc.createdBy] = (byUser[doc.createdBy] || 0) + 1;
        });
        
        return {
            total,
            byCategory,
            byUser,
            categories: Object.keys(byCategory).length
        };
    }

    // Export document
    exportDocument(documentId, format = 'txt') {
        const document = this.getDocument(documentId);
        if (!document) {
            return { success: false, message: 'Document not found' };
        }

        let content = '';
        switch (format) {
            case 'txt':
                content = `${document.name}\n\n${document.content}`;
                break;
            case 'html':
                content = `<html><head><title>${document.name}</title></head><body><h1>${document.name}</h1><div>${document.content.replace(/\n/g, '<br>')}</div></body></html>`;
                break;
            case 'json':
                content = JSON.stringify(document, null, 2);
                break;
        }

        return { success: true, content, filename: `${document.name}.${format}` };
    }
}

// Global document instance
const documentSystem = new DocumentSystem(); 
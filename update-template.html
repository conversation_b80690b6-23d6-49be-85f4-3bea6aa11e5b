<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM System - [Page Title]</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/bootstrap-custom.css">
</head>
<body>
    <!-- Navigation Container -->
    <div id="navigationContainer"></div>
    
    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row mb-4">
            <div class="col">
                <h2 class="mb-0">[Page Title]</h2>
                <p class="text-muted">[Page Description]</p>
            </div>
            <div class="col-auto">
                <!-- Action buttons if needed -->
                <button class="btn btn-primary">Primary Action</button>
            </div>
        </div>
        
        <!-- Card Example -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Card Title</h5>
            </div>
            <div class="card-body">
                <!-- Content goes here -->
            </div>
        </div>
        
        <!-- Table Example with Sorting/Filtering -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table id="dataTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Column 1</th>
                                <th>Column 2</th>
                                <th>Column 3</th>
                                <th class="no-sort">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Table rows populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Modal Example -->
        <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">Modal Title</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <!-- Modal content -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary">Save changes</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap 5 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom Scripts -->
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/common-ui.js"></script>
    <script src="js/table-utils.js"></script>
    <script src="js/[page-specific].js"></script>
    <script>
        // Initialize common UI
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize navigation with active page
            commonUI.initializeCommonUI('[page-name]');
            
            // Initialize sortable/filterable tables
            if (window.tableUtils) {
                tableUtils.initTable('dataTable', {
                    sortable: true,
                    filterable: true,
                    paginate: true,
                    pageSize: 10
                });
            }
        });
    </script>
</body>
</html>

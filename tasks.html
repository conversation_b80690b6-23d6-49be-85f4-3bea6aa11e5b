<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM System - Tasks</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/modern-ui.css">
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1>CRM System</h1>
            </div>
            <div class="user-info">
                <span id="username">User</span>
                <span id="userRole" class="user-role">Role</span>
                <button id="logoutBtn">Logout</button>
            </div>
        </header>
        
        <nav>
            <ul>
                <li><a href="dashboard.html">Dashboard</a></li>
                <li><a href="customers.html">Customers</a></li>
                <li><a href="contacts.html">Contacts</a></li>
                <li><a href="deals.html">Deals</a></li>
                <li><a href="tasks.html" class="active">Tasks</a></li>
                <li><a href="calendar.html">Calendar</a></li>
                <li><a href="reports.html">Reports</a></li>
                <li><a href="users.html" class="admin-only">Users</a></li>
                <li><a href="email.html">Email</a></li>
            </ul>
        </nav>
        
        <main>
            <h2>Task Management</h2>
            
            <div class="actions-bar">
                <button id="addTaskBtn" class="btn-primary">Add New Task</button>
                <div class="filter-box">
                    <select id="taskFilter">
                        <option value="all">All Tasks</option>
                        <option value="pending">Pending</option>
                        <option value="completed">Completed</option>
                    </select>
                </div>
            </div>
            
            <div class="task-form-container" id="taskFormContainer" style="display: none;">
                <h3 id="formTitle">Add New Task</h3>
                <form id="taskForm">
                    <input type="hidden" id="taskId">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="taskTitle">Task Title</label>
                            <input type="text" id="taskTitle" name="taskTitle" required>
                        </div>
                        <div class="form-group">
                            <label for="taskDueDate">Due Date</label>
                            <input type="date" id="taskDueDate" name="taskDueDate" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="taskPriority">Priority</label>
                            <select id="taskPriority" name="taskPriority" required>
                                <option value="Low">Low</option>
                                <option value="Medium">Medium</option>
                                <option value="High">High</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="taskRelatedTo">Related To</label>
                            <select id="taskRelatedTo" name="taskRelatedTo">
                                <option value="">None</option>
                                <option value="customer">Customer</option>
                                <option value="deal">Deal</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group" id="customerSelectGroup" style="display: none;">
                        <label for="taskCustomer">Customer</label>
                        <select id="taskCustomer" name="taskCustomer">
                            <option value="">Select Customer</option>
                            <!-- Customer options will be populated here -->
                        </select>
                    </div>
                    <div class="form-group" id="dealSelectGroup" style="display: none;">
                        <label for="taskDeal">Deal</label>
                        <select id="taskDeal" name="taskDeal">
                            <option value="">Select Deal</option>
                            <!-- Deal options will be populated here -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="taskDescription">Description</label>
                        <textarea id="taskDescription" name="taskDescription" rows="3"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">Save Task</button>
                        <button type="button" id="cancelTaskBtn" class="btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
            
            <div class="tasks-list">
                <h3>Pending Tasks</h3>
                <ul id="pendingTasksList">
                    <!-- Pending tasks will be populated here -->
                </ul>
                
                <h3>Completed Tasks</h3>
                <ul id="completedTasksList">
                    <!-- Completed tasks will be populated here -->
                </ul>
            </div>
        </main>
    </div>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/tasks.js"></script>
</body>
</html>
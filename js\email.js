// Email Integration System
class EmailSystem {
    constructor() {
        this.templates = this.loadTemplates();
        this.emailHistory = this.loadEmailHistory();
    }

    // Load email templates
    loadTemplates() {
        const templates = JSON.parse(localStorage.getItem('emailTemplates') || '[]');
        if (templates.length === 0) {
            const defaultTemplates = [
                {
                    id: 1,
                    name: 'Welcome Email',
                    subject: 'Welcome to our CRM system!',
                    body: `Dear {{customerName}},

Thank you for choosing our CRM system. We're excited to have you on board!

Here are some key features you can explore:
- Customer Management
- Deal Tracking
- Task Management
- Calendar Integration
- Advanced Reporting

If you have any questions, please don't hesitate to contact us.

Best regards,
{{userName}}`,
                    category: 'welcome'
                },
                {
                    id: 2,
                    name: 'Follow-up Email',
                    subject: 'Following up on our recent discussion',
                    body: `Dear {{customerName}},

I hope this email finds you well. I wanted to follow up on our recent discussion about {{dealTitle}}.

Here's a quick summary of what we discussed:
{{dealDescription}}

Next steps:
{{nextSteps}}

Please let me know if you have any questions or if there's anything else I can help you with.

Best regards,
{{userName}}`,
                    category: 'follow-up'
                },
                {
                    id: 3,
                    name: 'Meeting Reminder',
                    subject: 'Reminder: {{meetingTitle}}',
                    body: `Dear {{customerName}},

This is a friendly reminder about our upcoming meeting:

Meeting: {{meetingTitle}}
Date: {{meetingDate}}
Time: {{meetingTime}}
Location: {{meetingLocation}}

Agenda:
{{meetingAgenda}}

Please let me know if you need to reschedule or if you have any questions.

Best regards,
{{userName}}`,
                    category: 'reminder'
                }
            ];
            localStorage.setItem('emailTemplates', JSON.stringify(defaultTemplates));
            return defaultTemplates;
        }
        return templates;
    }

    // Load email history
    loadEmailHistory() {
        return JSON.parse(localStorage.getItem('emailHistory') || '[]');
    }

    // Send email
    sendEmail(to, subject, body, relatedTo = null, relatedId = null) {
        const email = {
            id: Date.now(),
            to: to,
            subject: subject,
            body: body,
            from: auth.currentUser.email,
            fromName: auth.currentUser.fullName,
            sentAt: new Date().toISOString(),
            status: 'sent',
            relatedTo: relatedTo,
            relatedId: relatedId,
            opened: false,
            clicked: false
        };

        this.emailHistory.push(email);
        localStorage.setItem('emailHistory', JSON.stringify(this.emailHistory));

        // In a real application, this would integrate with an email service
        console.log('Email sent:', email);
        
        return { success: true, emailId: email.id };
    }

    // Create email template
    createTemplate(templateData) {
        const template = {
            id: Date.now(),
            ...templateData,
            createdAt: new Date().toISOString(),
            createdBy: auth.currentUser.id
        };

        this.templates.push(template);
        localStorage.setItem('emailTemplates', JSON.stringify(this.templates));
        
        return { success: true, template };
    }

    // Update email template
    updateTemplate(templateId, updates) {
        const index = this.templates.findIndex(t => t.id == templateId);
        if (index !== -1) {
            this.templates[index] = { ...this.templates[index], ...updates };
            localStorage.setItem('emailTemplates', JSON.stringify(this.templates));
            return { success: true };
        }
        return { success: false, message: 'Template not found' };
    }

    // Delete email template
    deleteTemplate(templateId) {
        this.templates = this.templates.filter(t => t.id != templateId);
        localStorage.setItem('emailTemplates', JSON.stringify(this.templates));
        return { success: true };
    }

    // Get email template by ID
    getTemplate(templateId) {
        return this.templates.find(t => t.id == templateId);
    }

    // Get all templates
    getAllTemplates() {
        return this.templates;
    }

    // Get templates by category
    getTemplatesByCategory(category) {
        return this.templates.filter(t => t.category === category);
    }

    // Process template with variables
    processTemplate(template, variables) {
        let processedBody = template.body;
        let processedSubject = template.subject;

        // Replace variables in body and subject
        Object.keys(variables).forEach(key => {
            const regex = new RegExp(`{{${key}}}`, 'g');
            processedBody = processedBody.replace(regex, variables[key]);
            processedSubject = processedSubject.replace(regex, variables[key]);
        });

        return {
            subject: processedSubject,
            body: processedBody
        };
    }

    // Get email history
    getEmailHistory(limit = 50) {
        return this.emailHistory
            .sort((a, b) => new Date(b.sentAt) - new Date(a.sentAt))
            .slice(0, limit);
    }

    // Get emails by related entity
    getEmailsByEntity(entityType, entityId) {
        return this.emailHistory.filter(email => 
            email.relatedTo === entityType && email.relatedId == entityId
        );
    }

    // Track email open (simulated)
    trackEmailOpen(emailId) {
        const email = this.emailHistory.find(e => e.id == emailId);
        if (email) {
            email.opened = true;
            email.openedAt = new Date().toISOString();
            localStorage.setItem('emailHistory', JSON.stringify(this.emailHistory));
        }
    }

    // Track email click (simulated)
    trackEmailClick(emailId) {
        const email = this.emailHistory.find(e => e.id == emailId);
        if (email) {
            email.clicked = true;
            email.clickedAt = new Date().toISOString();
            localStorage.setItem('emailHistory', JSON.stringify(this.emailHistory));
        }
    }

    // Get email statistics
    getEmailStats() {
        const total = this.emailHistory.length;
        const opened = this.emailHistory.filter(e => e.opened).length;
        const clicked = this.emailHistory.filter(e => e.clicked).length;
        
        return {
            total,
            opened,
            clicked,
            openRate: total > 0 ? (opened / total * 100).toFixed(1) : 0,
            clickRate: total > 0 ? (clicked / total * 100).toFixed(1) : 0
        };
    }

    // Bulk email sending
    sendBulkEmail(recipients, templateId, variables = {}) {
        const template = this.getTemplate(templateId);
        if (!template) {
            return { success: false, message: 'Template not found' };
        }

        const results = [];
        recipients.forEach(recipient => {
            const processed = this.processTemplate(template, {
                ...variables,
                customerName: recipient.name,
                customerEmail: recipient.email
            });

            const result = this.sendEmail(
                recipient.email,
                processed.subject,
                processed.body,
                recipient.relatedTo,
                recipient.relatedId
            );

            results.push(result);
        });

        return { success: true, results };
    }
}

// Global email instance
const emailSystem = new EmailSystem(); 
// Customers functionality
document.addEventListener('DOMContentLoaded', async function() {
    // Check authentication
    if (!requireAuth()) return;

    // State management
    let customers = [];
    let currentFilter = {};
    let editingCustomerId = null;

    // UI elements
    const addCustomerBtn = document.getElementById('addCustomerBtn');
    const customerForm = document.getElementById('customerForm');
    const customerFormModal = new bootstrap.Modal(document.getElementById('customerFormModal'));
    const customerFormModalLabel = document.getElementById('customerFormModalLabel');
    const customerSearch = document.getElementById('customerSearch');
    const searchBtn = document.getElementById('searchBtn');
    const customersTableBody = document.getElementById('customersTableBody');
    const tableContainer = document.querySelector('.table-responsive');

    // Subscribe to loading state changes
    api.onLoadingChange('customers', (isLoading) => {
        if (isLoading) {
            showLoading('tableContainer', 'Loading customers...');
        }
    });

    // Load customers
    await loadCustomers();
    
    // Load customers from API
    async function loadCustomers() {
        try {
            const response = await api.getCustomers(currentFilter);
            customers = response.data || response.customers || response;
            displayCustomers();
            autoTagVIPCustomers();
        } catch (error) {
            console.error('Failed to load customers:', error);
            showError('tableContainer', error);
            // Fallback to local data
            loadLocalCustomers();
        }
    }

    // Load customers from local storage as fallback
    function loadLocalCustomers() {
        customers = JSON.parse(localStorage.getItem('customers') || '[]');
        if (customers.length === 0) {
            customers = [
                { id: 1, name: 'John Doe', email: '<EMAIL>', phone: '************', company: 'ABC Corp', address: '123 Main St, City, State', createdAt: new Date().toISOString() },
                { id: 2, name: 'Jane Smith', email: '<EMAIL>', phone: '************', company: 'XYZ Inc', address: '456 Oak Ave, City, State', createdAt: new Date().toISOString() },
                { id: 3, name: 'Bob Johnson', email: '<EMAIL>', phone: '************', company: 'DEF Ltd', address: '789 Pine Rd, City, State', createdAt: new Date().toISOString() },
                { id: 4, name: 'Alice Brown', email: '<EMAIL>', phone: '************', company: 'GHI Co', address: '321 Elm St, City, State', createdAt: new Date().toISOString() }
            ];
            localStorage.setItem('customers', JSON.stringify(customers));
        }
        displayCustomers();
    }
    
    // Display customers
    function displayCustomers(customersToShow = customers) {
        if (!customersTableBody) return;
        
        if (customersToShow.length === 0) {
            customersTableBody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-muted p-4">
                        <i class="bi bi-inbox fs-1"></i>
                        <p class="mt-2">No customers found</p>
                    </td>
                </tr>
            `;
            return;
        }

        customersTableBody.innerHTML = '';
        customersToShow.forEach(customer => {
            const row = document.createElement('tr');
            const tags = customer.tags ? customer.tags.map(t => 
                `<span class="badge bg-secondary me-1">${t}</span>`
            ).join('') : '';
            
            // Calculate lead score
            const score = calculateLeadScore(customer);
            const scoreClass = score > 70 ? 'text-success' : score > 40 ? 'text-warning' : 'text-muted';
            
            row.innerHTML = `
                <td>
                    <div class="d-flex align-items-center">
                        <div class="avatar-sm me-2">
                            <span class="avatar-title rounded-circle bg-primary">
                                ${customer.name.charAt(0).toUpperCase()}
                            </span>
                        </div>
                        <div>
                            <div class="fw-semibold">${customer.name}</div>
                            <small class="text-muted">${customer.company || 'No company'}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <a href="mailto:${customer.email}" class="text-decoration-none">
                        <i class="bi bi-envelope me-1"></i>${customer.email}
                    </a>
                </td>
                <td>
                    <a href="tel:${customer.phone}" class="text-decoration-none">
                        <i class="bi bi-telephone me-1"></i>${customer.phone}
                    </a>
                </td>
                <td>${tags}</td>
                <td class="text-center">
                    <span class="badge ${scoreClass}" title="Lead Score">
                        <i class="bi bi-graph-up me-1"></i>${score}
                    </span>
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-outline-primary" onclick="editCustomer(${customer.id})" title="Edit">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="showTimeline(${customer.id})" title="Timeline">
                            <i class="bi bi-clock-history"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="sendEmail('${customer.email}')" title="Email">
                            <i class="bi bi-envelope"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteCustomer(${customer.id})" title="Delete">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            customersTableBody.appendChild(row);
        });
    }

    // Calculate lead score based on various factors
    function calculateLeadScore(customer) {
        let score = 50; // Base score
        
        // Has company info
        if (customer.company) score += 10;
        
        // Has tags
        if (customer.tags && customer.tags.length > 0) {
            score += customer.tags.length * 5;
            if (customer.tags.includes('VIP')) score += 20;
        }
        
        // Recent activity (created within last 30 days)
        const daysSinceCreated = (Date.now() - new Date(customer.createdAt)) / (1000 * 60 * 60 * 24);
        if (daysSinceCreated < 30) score += 15;
        
        // Has complete contact info
        if (customer.phone && customer.address) score += 10;
        
        return Math.min(100, score);
    }

    // Auto-tag VIP customers based on criteria
    async function autoTagVIPCustomers() {
        let hasUpdates = false;
        const updatedCustomers = [];
        
        for (const customer of customers) {
            // Check if customer qualifies for VIP tag
            const score = calculateLeadScore(customer);
            const isVIP = score > 80;
            const hasVIPTag = customer.tags && customer.tags.includes('VIP');
            
            if (isVIP && !hasVIPTag) {
                customer.tags = customer.tags ? [...customer.tags, 'VIP'] : ['VIP'];
                updatedCustomers.push(customer);
                hasUpdates = true;
            }
        }
        
        if (hasUpdates) {
            // Update customers via API
            for (const customer of updatedCustomers) {
                try {
                    await api.updateCustomer(customer.id, customer);
                } catch (error) {
                    console.error(`Failed to update customer ${customer.id}:`, error);
                }
            }
            
            // Refresh display
            displayCustomers();
            showToast(`${updatedCustomers.length} customers auto-tagged as VIP`, 'success');
        }
    }
    
    // Show form
    function showForm(mode = 'add', customer = null) {
        if (mode === 'add') {
            customerFormModalLabel.textContent = 'Add New Customer';
            customerForm.reset();
            document.getElementById('customerId').value = '';
            document.getElementById('customerTags').value = '';
        } else {
            customerFormModalLabel.textContent = 'Edit Customer';
            document.getElementById('customerId').value = customer.id;
            document.getElementById('customerName').value = customer.name;
            document.getElementById('customerEmail').value = customer.email;
            document.getElementById('customerPhone').value = customer.phone || '';
            document.getElementById('customerCompany').value = customer.company || '';
            document.getElementById('customerAddress').value = customer.address || '';
            document.getElementById('customerTags').value = customer.tags ? customer.tags.join(', ') : '';
        }
        customerFormModal.show();
    }
    
    // Hide form
    function hideForm() {
        customerFormModal.hide();
        customerForm.reset();
    }
    
    // Add customer button
    if (addCustomerBtn) {
        addCustomerBtn.addEventListener('click', () => showForm('add'));
    }
    
    // Form submission
    if (customerForm) {
        customerForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = getFormData(customerForm);
            const customerId = document.getElementById('customerId').value;
            const tagsInput = formData.customerTags || '';
            
            // Prepare customer data
            const customerData = {
                name: formData.customerName,
                email: formData.customerEmail,
                phone: formData.customerPhone,
                company: formData.customerCompany,
                address: formData.customerAddress,
                tags: tagsInput ? tagsInput.split(',').map(t => t.trim()).filter(t => t) : []
            };
            
            // Validate required fields
            const validationErrors = [];
            if (!validateRequired(customerData.name)) {
                validationErrors.push('Name is required');
            }
            if (!validateRequired(customerData.email)) {
                validationErrors.push('Email is required');
            } else if (!validateEmail(customerData.email)) {
                validationErrors.push('Please enter a valid email address');
            }
            if (customerData.phone && !validatePhone(customerData.phone)) {
                validationErrors.push('Please enter a valid phone number');
            }
            
            if (validationErrors.length > 0) {
                showError('formMessage', { message: validationErrors.join('<br>') });
                return;
            }
            
            // Show loading state
            const submitBtn = customerForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Saving...';
            
            try {
                let response;
                if (customerId) {
                    // Update existing customer
                    response = await api.updateCustomer(customerId, customerData);
                    showToast('Customer updated successfully!', 'success');
                } else {
                    // Create new customer
                    response = await api.createCustomer(customerData);
                    showToast('Customer created successfully!', 'success');
                }
                
                // Refresh customers list
                await loadCustomers();
                hideForm();
                
            } catch (error) {
                console.error('Failed to save customer:', error);
                showError('formMessage', error);
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        });
    }
    
    // Search functionality
    if (customerSearch && searchBtn) {
        let searchTimeout;
        
        async function performSearch() {
            const searchTerm = customerSearch.value.trim();
            
            if (searchTerm) {
                currentFilter.search = searchTerm;
            } else {
                delete currentFilter.search;
            }
            
            await loadCustomers();
        }
        
        searchBtn.addEventListener('click', performSearch);
        
        // Debounced search on typing
        customerSearch.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(performSearch, 300);
        });
        
        customerSearch.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                clearTimeout(searchTimeout);
                performSearch();
            }
        });
    }
    
    // Global functions for edit and delete
    window.editCustomer = async function(id) {
        try {
            const customer = await api.getCustomer(id);
            if (customer) {
                showForm('edit', customer);
            }
        } catch (error) {
            console.error('Failed to load customer:', error);
            // Fallback to local data
            const customer = customers.find(c => c.id == id);
            if (customer) {
                showForm('edit', customer);
            } else {
                showToast('Customer not found', 'danger');
            }
        }
    };
    
    window.deleteCustomer = async function(id) {
        const confirmed = await showConfirmDialog(
            'Delete Customer',
            'Are you sure you want to delete this customer? This action cannot be undone.'
        );
        
        if (confirmed) {
            try {
                await api.deleteCustomer(id);
                showToast('Customer deleted successfully!', 'success');
                await loadCustomers();
            } catch (error) {
                console.error('Failed to delete customer:', error);
                showToast('Failed to delete customer', 'danger');
            }
        }
    };
    
    // Email functionality
    window.sendEmail = function(email) {
        window.location.href = `mailto:${email}`;
    };
    
    // Show confirm dialog
    async function showConfirmDialog(title, message) {
        return new Promise((resolve) => {
            if (window.bootstrap) {
                // Use Bootstrap modal if available
                const modal = document.createElement('div');
                modal.innerHTML = `
                    <div class="modal fade" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">${title}</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <p>${message}</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-danger" id="confirmBtn">Delete</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
                
                const bsModal = new bootstrap.Modal(modal.querySelector('.modal'));
                modal.querySelector('#confirmBtn').addEventListener('click', () => {
                    resolve(true);
                    bsModal.hide();
                });
                modal.querySelector('.modal').addEventListener('hidden.bs.modal', () => {
                    modal.remove();
                    resolve(false);
                });
                bsModal.show();
            } else {
                // Fallback to native confirm
                resolve(confirm(message));
            }
        });
    }

    // Timeline modal logic
    const timelineModal = document.getElementById('timelineModal');
    const closeTimelineModal = document.getElementById('closeTimelineModal');
    const timelineList = document.getElementById('timelineList');
    let currentTimelineCustomerId = null;

    window.showTimeline = function(id) {
        currentTimelineCustomerId = id;
        renderTimeline();
        timelineModal.style.display = 'block';
    };

    function renderTimeline() {
        const notesData = JSON.parse(localStorage.getItem('customerNotes') || '{}');
        const attachmentsData = JSON.parse(localStorage.getItem('customerAttachments') || '{}');
        const customer = customers.find(c => c.id == currentTimelineCustomerId);
        let timeline = [];
        if (customer) {
            timeline.push({ type: 'created', text: 'Customer created', date: customer.createdAt });
            if (customer.updatedAt) timeline.push({ type: 'updated', text: 'Customer updated', date: customer.updatedAt });
        }
        (notesData[currentTimelineCustomerId] || []).forEach(n => timeline.push({ type: 'note', text: n.text, date: n.date }));
        (attachmentsData[currentTimelineCustomerId] || []).forEach(a => timeline.push({ type: 'attachment', text: 'File uploaded: ' + a.name, date: '' }));
        timeline.sort((a, b) => new Date(a.date) - new Date(b.date));
        timelineList.innerHTML = timeline.length ? timeline.map(item => `<div class='timeline-item'><strong>${item.type}:</strong> ${item.text} <span style='font-size:10px;color:#888;'>${item.date}</span></div>`).join('') : '<em>No activity yet.</em>';
    }

    if (closeTimelineModal) {
        closeTimelineModal.onclick = function() {
            timelineModal.style.display = 'none';
        };
    }

    // Attachments modal logic
    const attachmentsModal = document.getElementById('attachmentsModal');
    const closeAttachmentsModal = document.getElementById('closeAttachmentsModal');
    const attachmentInput = document.getElementById('attachmentInput');
    const uploadAttachmentBtn = document.getElementById('uploadAttachmentBtn');
    const attachmentsList = document.getElementById('attachmentsList');
    let currentAttachmentCustomerId = null;

    window.showAttachments = function(id) {
        currentAttachmentCustomerId = id;
        renderAttachments();
        attachmentsModal.style.display = 'block';
    };

    function renderAttachments() {
        const attachmentsData = JSON.parse(localStorage.getItem('customerAttachments') || '{}');
        const files = attachmentsData[currentAttachmentCustomerId] || [];
        attachmentsList.innerHTML = files.length ? files.map((f, i) => `<div class='attachment-item'><a href='${f.data}' download='${f.name}'>${f.name}</a></div>`).join('') : '<em>No attachments yet.</em>';
    }

    if (closeAttachmentsModal) {
        closeAttachmentsModal.onclick = function() {
            attachmentsModal.style.display = 'none';
        };
    }

    if (uploadAttachmentBtn) {
        uploadAttachmentBtn.onclick = function() {
            const files = attachmentInput.files;
            if (!files.length) return;
            const attachmentsData = JSON.parse(localStorage.getItem('customerAttachments') || '{}');
            if (!attachmentsData[currentAttachmentCustomerId]) attachmentsData[currentAttachmentCustomerId] = [];
            Array.from(files).forEach(file => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    attachmentsData[currentAttachmentCustomerId].push({ name: file.name, data: e.target.result });
                    localStorage.setItem('customerAttachments', JSON.stringify(attachmentsData));
                    renderAttachments();
                };
                reader.readAsDataURL(file);
            });
        };
    }

    // Send Email logic
    window.sendEmail = function(email) {
        window.location.href = `mailto:${email}`;
    };

    // Notes modal logic
    const notesModal = document.getElementById('notesModal');
    const closeNotesModal = document.getElementById('closeNotesModal');
    const notesList = document.getElementById('notesList');
    const newNoteText = document.getElementById('newNoteText');
    const saveNoteBtn = document.getElementById('saveNoteBtn');
    let currentCustomerId = null;

    window.showNotes = function(id) {
        currentCustomerId = id;
        const notesData = JSON.parse(localStorage.getItem('customerNotes') || '{}');
        const notes = notesData[id] || [];
        notesList.innerHTML = notes.length ? notes.map(n => `<div class="note-item">${n.text} <span style='font-size:10px;color:#888;'>${n.date}</span></div>`).join('') : '<em>No notes yet.</em>';
        newNoteText.value = '';
        notesModal.style.display = 'block';
    };

    if (closeNotesModal) {
        closeNotesModal.onclick = function() {
            notesModal.style.display = 'none';
        };
    }

    if (saveNoteBtn) {
        saveNoteBtn.onclick = function() {
            const text = newNoteText.value.trim();
            if (!text) return;
            const notesData = JSON.parse(localStorage.getItem('customerNotes') || '{}');
            if (!notesData[currentCustomerId]) notesData[currentCustomerId] = [];
            notesData[currentCustomerId].push({ text, date: new Date().toLocaleString() });
            localStorage.setItem('customerNotes', JSON.stringify(notesData));
            showNotes(currentCustomerId);
        };
    }
});

function showMessage(message, type) {
    // Show notification in notification area
    const notificationArea = document.getElementById('notificationArea');
    if (notificationArea) {
        const note = document.createElement('div');
        note.className = `notification ${type}`;
        note.textContent = message;
        notificationArea.appendChild(note);
        notificationArea.style.display = 'block';
        setTimeout(() => {
            if (note.parentNode) note.remove();
            if (!notificationArea.hasChildNodes()) notificationArea.style.display = 'none';
        }, 4000);
    } else {
        // fallback to old message style
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            ${type === 'error' ? 'background-color: #e74c3c;' : 'background-color: #27ae60;'}
        `;
        document.body.appendChild(messageDiv);
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 3000);
    }
    // Notification bell toggle
    const notificationBell = document.getElementById('notificationBell');
    let notificationAreaDom = document.getElementById('notificationArea');
    if (notificationBell && notificationAreaDom) {
        notificationBell.onclick = function() {
            notificationAreaDom.style.display = notificationAreaDom.style.display === 'none' ? 'block' : 'none';
        };
    }
}
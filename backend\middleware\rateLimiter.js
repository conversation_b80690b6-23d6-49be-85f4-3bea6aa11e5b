/**
 * Rate Limiting Middleware
 * 
 * Implements different rate limiting strategies for various endpoints
 * to prevent abuse and ensure fair usage of the API
 */

const rateLimit = require('express-rate-limit');
const RedisStore = require('rate-limit-redis');
const Redis = require('ioredis');

// Redis client for distributed rate limiting (optional)
let redisClient = null;
if (process.env.REDIS_URL) {
  redisClient = new Redis(process.env.REDIS_URL);
}

/**
 * Create a rate limiter with custom options
 * @param {Object} options - Rate limiter options
 * @returns {Function} Express middleware
 */
const createLimiter = (options) => {
  const config = {
    windowMs: 15 * 60 * 1000, // 15 minutes default
    max: 100, // Limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true, // Return rate limit info in `RateLimit-*` headers
    legacyHeaders: false, // Disable `X-RateLimit-*` headers
    handler: (req, res) => {
      res.status(429).json({
        success: false,
        message: options.message || 'Too many requests, please try again later.',
        retryAfter: new Date(Date.now() + req.rateLimit.resetTime).toISOString()
      });
    },
    ...options
  };

  // Use Redis store if available for distributed rate limiting
  if (redisClient) {
    config.store = new RedisStore({
      client: redisClient,
      prefix: 'rl:'
    });
  }

  return rateLimit(config);
};

/**
 * General API rate limiter
 * 100 requests per 15 minutes per IP
 */
const generalLimiter = createLimiter({
  windowMs: 15 * 60 * 1000,
  max: 100,
  message: 'Too many API requests from this IP, please try again later.'
});

/**
 * Strict rate limiter for authentication endpoints
 * 5 requests per 15 minutes per IP
 */
const authLimiter = createLimiter({
  windowMs: 15 * 60 * 1000,
  max: 5,
  skipSuccessfulRequests: true, // Don't count successful requests
  message: 'Too many authentication attempts, please try again later.'
});

/**
 * Rate limiter for password reset
 * 3 requests per hour per IP
 */
const passwordResetLimiter = createLimiter({
  windowMs: 60 * 60 * 1000,
  max: 3,
  message: 'Too many password reset requests, please try again later.'
});

/**
 * Rate limiter for account creation
 * 5 requests per hour per IP
 */
const createAccountLimiter = createLimiter({
  windowMs: 60 * 60 * 1000,
  max: 5,
  message: 'Too many account creation attempts, please try again later.'
});

/**
 * Rate limiter for file uploads
 * 10 uploads per hour per IP
 */
const uploadLimiter = createLimiter({
  windowMs: 60 * 60 * 1000,
  max: 10,
  message: 'Too many file uploads, please try again later.'
});

/**
 * Rate limiter for report generation
 * 20 requests per hour per IP
 */
const reportLimiter = createLimiter({
  windowMs: 60 * 60 * 1000,
  max: 20,
  message: 'Too many report generation requests, please try again later.'
});

/**
 * Dynamic rate limiter based on user role
 * Premium users get higher limits
 */
const dynamicLimiter = (req, res, next) => {
  let maxRequests = 100;
  
  if (req.user) {
    switch (req.user.role) {
      case 'admin':
        maxRequests = 1000;
        break;
      case 'manager':
        maxRequests = 500;
        break;
      case 'premium':
        maxRequests = 300;
        break;
      default:
        maxRequests = 100;
    }
  }

  const limiter = createLimiter({
    windowMs: 15 * 60 * 1000,
    max: maxRequests,
    keyGenerator: (req) => req.user ? `user-${req.user.id}` : req.ip
  });

  limiter(req, res, next);
};

module.exports = {
  generalLimiter,
  authLimiter,
  passwordResetLimiter,
  createAccountLimiter,
  uploadLimiter,
  reportLimiter,
  dynamicLimiter,
  createLimiter
};

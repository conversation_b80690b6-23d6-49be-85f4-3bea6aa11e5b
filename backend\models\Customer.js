const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Customer = sequelize.define('Customer', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  companyName: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true
    }
  },
  contactFirstName: {
    type: DataTypes.STRING,
    allowNull: false
  },
  contactLastName: {
    type: DataTypes.STRING,
    allowNull: false
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      isEmail: true
    }
  },
  phone: {
    type: DataTypes.STRING,
    allowNull: true
  },
  mobile: {
    type: DataTypes.STRING,
    allowNull: true
  },
  address: {
    type: DataTypes.STRING,
    allowNull: true
  },
  city: {
    type: DataTypes.STRING,
    allowNull: true
  },
  state: {
    type: DataTypes.STRING,
    allowNull: true
  },
  country: {
    type: DataTypes.STRING,
    allowNull: true
  },
  postalCode: {
    type: DataTypes.STRING,
    allowNull: true
  },
  website: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      isUrl: true
    }
  },
  industry: {
    type: DataTypes.STRING,
    allowNull: true
  },
  annualRevenue: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true
  },
  numberOfEmployees: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  leadSource: {
    type: DataTypes.ENUM('website', 'referral', 'social_media', 'cold_call', 'trade_show', 'other'),
    defaultValue: 'website'
  },
  status: {
    type: DataTypes.ENUM('lead', 'prospect', 'customer', 'inactive'),
    defaultValue: 'lead'
  },
  rating: {
    type: DataTypes.ENUM('hot', 'warm', 'cold'),
    defaultValue: 'warm'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  tags: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    defaultValue: []
  },
  assignedToId: {
    type: DataTypes.UUID,
    allowNull: true
  },
  createdById: {
    type: DataTypes.UUID,
    allowNull: true
  }
}, {
  timestamps: true
});

module.exports = Customer;

const express = require('express');
const {
  getCustomers,
  getCustomer,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerStats
} = require('../controllers/customerController');

const { protect, authorize } = require('../middleware/auth');
const asyncHandler = require('../middleware/asyncHandler');
const {
  validateUUID,
  validateEmail,
  validatePhone,
  validateRequired,
  validateOptional,
  validateEnum,
  validatePagination,
  handleValidationErrors,
  body
} = require('../middleware/validation');

const router = express.Router();

// Validation rules
const validateCreateCustomer = [
  validateRequired('companyName', 'Company name is required'),
  validateEmail(),
  validatePhone('phone'),
  validateOptional('website'),
  validateOptional('industry'),
  validateEnum('status', ['lead', 'active', 'inactive'], 'Invalid status'),
  validateEnum('rating', ['hot', 'warm', 'cold'], 'Invalid rating'),
  body('annualRevenue')
    .optional()
    .isNumeric().withMessage('Annual revenue must be a number'),
  body('employees')
    .optional()
    .isInt({ min: 0 }).withMessage('Employees must be a positive number'),
  body('assignedToId')
    .optional()
    .isUUID().withMessage('Assigned to must be a valid user ID')
];

const validateUpdateCustomer = [
  validateOptional('companyName'),
  body('email')
    .optional()
    .isEmail().withMessage('Please provide a valid email')
    .normalizeEmail(),
  validatePhone('phone'),
  validateOptional('website'),
  validateOptional('industry'),
  validateEnum('status', ['lead', 'active', 'inactive'], 'Invalid status'),
  validateEnum('rating', ['hot', 'warm', 'cold'], 'Invalid rating'),
  body('annualRevenue')
    .optional()
    .isNumeric().withMessage('Annual revenue must be a number'),
  body('employees')
    .optional()
    .isInt({ min: 0 }).withMessage('Employees must be a positive number'),
  body('assignedToId')
    .optional()
    .isUUID().withMessage('Assigned to must be a valid user ID')
];

// All routes require authentication
router.use(protect);

// Statistics route
router.get('/stats', asyncHandler(getCustomerStats));

// CRUD routes
router
  .route('/')
  .get(validatePagination(), handleValidationErrors, asyncHandler(getCustomers))
  .post(validateCreateCustomer, handleValidationErrors, asyncHandler(createCustomer));

router
  .route('/:id')
  .get(validateUUID('id'), handleValidationErrors, asyncHandler(getCustomer))
  .put(validateUUID('id'), validateUpdateCustomer, handleValidationErrors, asyncHandler(updateCustomer))
  .delete(validateUUID('id'), handleValidationErrors, authorize('admin', 'manager'), asyncHandler(deleteCustomer));

module.exports = router;

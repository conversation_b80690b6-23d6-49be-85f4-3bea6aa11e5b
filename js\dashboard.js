// Dashboard functionality
document.addEventListener('DOMContentLoaded', async function() {
    // Check authentication
    if (!requireAuth()) return;

    // Display current user info
    const currentUser = getCurrentUser();
    if (currentUser) {
        const userNameEl = document.getElementById('userName');
        const userRoleEl = document.getElementById('userRole');
        if (userNameEl) userNameEl.textContent = currentUser.fullName || currentUser.name;
        if (userRoleEl) userRoleEl.textContent = currentUser.role || 'User';
    }

    // Logout functionality
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', async function() {
            try {
                await api.logout();
            } catch (error) {
                console.error('Logout error:', error);
                // Force logout even if API fails
                localStorage.clear();
                window.location.href = '/index.html';
            }
        });
    }

    // Load dashboard stats
    await loadDashboardStats();
    
    // Set up auto-refresh
    setInterval(loadDashboardStats, 60000); // Refresh every minute
});

async function loadDashboardStats() {
    try {
        // Show loading state
        showLoading('statsContainer', 'Loading dashboard data...');
        
        // Fetch dashboard stats from API
        const stats = await api.getDashboardStats();
        
        // Update UI with fetched data
        updateStatsDisplay(stats);
        
        // Load recent activities
        await loadRecentActivities();
        
    } catch (error) {
        console.error('Failed to load dashboard stats:', error);
        
        // Fallback to local data if API fails
        loadLocalDashboardData();
    }
}

function updateStatsDisplay(stats) {
    // Update stat cards
    const totalCustomersEl = document.getElementById('totalCustomers');
    const activeDealsEl = document.getElementById('activeDeals');
    const pendingTasksEl = document.getElementById('pendingTasks');
    const upcomingEventsEl = document.getElementById('upcomingEvents');
    
    if (totalCustomersEl) {
        animateValue(totalCustomersEl, parseInt(totalCustomersEl.textContent) || 0, stats.totalCustomers || 0, 1000);
    }
    if (activeDealsEl) {
        animateValue(activeDealsEl, parseInt(activeDealsEl.textContent) || 0, stats.activeDeals || 0, 1000);
    }
    if (pendingTasksEl) {
        animateValue(pendingTasksEl, parseInt(pendingTasksEl.textContent) || 0, stats.pendingTasks || 0, 1000);
    }
    if (upcomingEventsEl) {
        animateValue(upcomingEventsEl, parseInt(upcomingEventsEl.textContent) || 0, stats.upcomingEvents || 0, 1000);
    }
    
    // Update revenue if available
    const revenueEl = document.getElementById('totalRevenue');
    if (revenueEl && stats.totalRevenue !== undefined) {
        revenueEl.textContent = formatCurrency(stats.totalRevenue);
    }
    
    // Update conversion rate if available
    const conversionEl = document.getElementById('conversionRate');
    if (conversionEl && stats.conversionRate !== undefined) {
        conversionEl.textContent = `${stats.conversionRate}%`;
    }
}

function animateValue(element, start, end, duration) {
    const range = end - start;
    let current = start;
    const increment = end > start ? 1 : -1;
    const stepTime = Math.abs(Math.floor(duration / range));
    
    const timer = setInterval(() => {
        current += increment;
        element.textContent = current;
        if (current === end) {
            clearInterval(timer);
        }
    }, stepTime);
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

async function loadRecentActivities() {
    const recentActivityList = document.getElementById('recentActivityList');
    if (!recentActivityList) return;
    
    try {
        // Clear existing activities
        recentActivityList.innerHTML = '<li class="text-center"><span class="spinner-border spinner-border-sm"></span> Loading activities...</li>';
        
        // Fetch recent activities from API
        const activities = await api.getActivities({ limit: 10 });
        
        // Clear loading message
        recentActivityList.innerHTML = '';
        
        if (activities && activities.length > 0) {
            activities.forEach(activity => {
                const li = document.createElement('li');
                li.className = 'list-group-item d-flex justify-content-between align-items-start';
                li.innerHTML = `
                    <div class="ms-2 me-auto">
                        <div class="fw-bold">${activity.type}</div>
                        ${activity.description}
                    </div>
                    <small class="text-muted">${formatRelativeTime(activity.createdAt)}</small>
                `;
                recentActivityList.appendChild(li);
            });
        } else {
            recentActivityList.innerHTML = '<li class="list-group-item text-muted">No recent activities</li>';
        }
    } catch (error) {
        console.error('Failed to load activities:', error);
        recentActivityList.innerHTML = '<li class="list-group-item text-danger">Failed to load activities</li>';
    }
}

function formatRelativeTime(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'just now';
    if (diffMins < 60) return `${diffMins} minutes ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours} hours ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    if (diffDays < 7) return `${diffDays} days ago`;
    
    return date.toLocaleDateString();
}

function loadLocalDashboardData() {
    // Fallback: Load data from localStorage
    const customers = JSON.parse(localStorage.getItem('customers') || '[]');
    const deals = JSON.parse(localStorage.getItem('deals') || '[]');
    const tasks = JSON.parse(localStorage.getItem('tasks') || '[]');
    const events = JSON.parse(localStorage.getItem('events') || '[]');
    
    // Update dashboard stats with local data
    document.getElementById('totalCustomers').textContent = customers.length;
    document.getElementById('activeDeals').textContent = deals.filter(deal => deal.stage === 'active').length;
    document.getElementById('pendingTasks').textContent = tasks.filter(task => !task.completed).length;
    document.getElementById('upcomingEvents').textContent = events.filter(event => new Date(event.date) > new Date()).length;
    
    // Generate recent activity from local data
    const recentActivityList = document.getElementById('recentActivityList');
    if (recentActivityList) {
        const activities = [];
        
        // Add recent customer activities
        customers.slice(-3).forEach(customer => {
            activities.push({
                description: `${customer.name} was added as a new customer`,
                createdAt: customer.createdAt,
                type: 'New Customer'
            });
        });
        
        // Add recent deal activities
        deals.slice(-2).forEach(deal => {
            activities.push({
                description: `Deal "${deal.title}" was ${deal.stage || deal.status}`,
                createdAt: deal.updatedAt || deal.createdAt,
                type: 'Deal Update'
            });
        });
        
        // Add recent task activities
        tasks.filter(task => task.completed).slice(-2).forEach(task => {
            activities.push({
                description: `Task "${task.title}" was completed`,
                createdAt: task.completedAt || task.updatedAt,
                type: 'Task Completed'
            });
        });
        
        // Sort activities by time (most recent first)
        activities.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        
        // Clear existing activities
        recentActivityList.innerHTML = '';
        
        // Display activities
        if (activities.length > 0) {
            activities.slice(0, 5).forEach(activity => {
                const li = document.createElement('li');
                li.className = 'list-group-item d-flex justify-content-between align-items-start';
                li.innerHTML = `
                    <div class="ms-2 me-auto">
                        <div class="fw-bold">${activity.type}</div>
                        ${activity.description}
                    </div>
                    <small class="text-muted">${formatRelativeTime(activity.createdAt)}</small>
                `;
                recentActivityList.appendChild(li);
            });
        } else {
            recentActivityList.innerHTML = '<li class="list-group-item text-muted">No recent activities</li>';
        }
    }
}
    
// Initialize sample data if needed
function initializeSampleData() {
    const customers = JSON.parse(localStorage.getItem('customers') || '[]');
    const deals = JSON.parse(localStorage.getItem('deals') || '[]');
    const tasks = JSON.parse(localStorage.getItem('tasks') || '[]');
    const events = JSON.parse(localStorage.getItem('events') || '[]');
    
    if (customers.length === 0) {
        const sampleCustomers = [
            { id: 1, name: 'John Doe', email: '<EMAIL>', phone: '************', company: 'ABC Corp', createdAt: new Date().toISOString() },
            { id: 2, name: 'Jane Smith', email: '<EMAIL>', phone: '************', company: 'XYZ Inc', createdAt: new Date().toISOString() }
        ];
        localStorage.setItem('customers', JSON.stringify(sampleCustomers));
    }
    
    if (deals.length === 0) {
        const sampleDeals = [
            { id: 1, title: 'ABC Corp Contract', value: 50000, stage: 'active', customer: 'John Doe', createdAt: new Date().toISOString() },
            { id: 2, title: 'XYZ Inc Partnership', value: 75000, stage: 'negotiation', customer: 'Jane Smith', createdAt: new Date().toISOString() }
        ];
        localStorage.setItem('deals', JSON.stringify(sampleDeals));
    }
    
    if (tasks.length === 0) {
        const sampleTasks = [
            { id: 1, title: 'Follow up with leads', description: 'Contact potential customers', priority: 'high', completed: false, createdAt: new Date().toISOString() },
            { id: 2, title: 'Prepare presentation', description: 'Create slides for client meeting', priority: 'medium', completed: false, createdAt: new Date().toISOString() }
        ];
        localStorage.setItem('tasks', JSON.stringify(sampleTasks));
    }
    
    if (events.length === 0) {
        const sampleEvents = [
            { id: 1, title: 'Client Meeting', date: new Date(Date.now() + 86400000).toISOString().split('T')[0], startTime: '10:00', endTime: '11:00', description: 'Meeting with ABC Corp', createdAt: new Date().toISOString() },
            { id: 2, title: 'Team Standup', date: new Date(Date.now() + 172800000).toISOString().split('T')[0], startTime: '09:00', endTime: '09:30', description: 'Daily team meeting', createdAt: new Date().toISOString() }
        ];
        localStorage.setItem('events', JSON.stringify(sampleEvents));
    }
}

// Initialize quick actions
function initializeQuickActions() {
    const quickActions = [
        { id: 'newCustomerBtn', href: 'customers.html', icon: 'bi-person-plus' },
        { id: 'newDealBtn', href: 'deals.html', icon: 'bi-briefcase' },
        { id: 'newTaskBtn', href: 'tasks.html', icon: 'bi-check-square' },
        { id: 'newEventBtn', href: 'calendar.html', icon: 'bi-calendar-event' }
    ];
    
    quickActions.forEach(action => {
        const button = document.getElementById(action.id);
        if (button) {
            button.addEventListener('click', function() {
                // Animate button click
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                    window.location.href = action.href;
                }, 150);
            });
        }
    });
}

// Initialize on page load
initializeSampleData();
initializeQuickActions();

// Contacts functionality
document.addEventListener('DOMContentLoaded', function() {
    // Logout functionality
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function() {
            auth.logout();
        });
    }
    
    // Initialize contacts data
    let contacts = JSON.parse(localStorage.getItem('contacts') || '[]');
    
    // Add sample data if empty
    if (contacts.length === 0) {
        contacts = [
            { id: 1, name: '<PERSON>', email: '<EMAIL>', phone: '************', customer: 'ABC Corp', position: 'Marketing Manager', notes: 'Key decision maker for marketing campaigns', createdAt: new Date().toISOString() },
            { id: 2, name: '<PERSON>', email: '<EMAIL>', phone: '************', customer: 'XYZ Inc', position: 'CTO', notes: 'Technical contact for software implementation', createdAt: new Date().toISOString() },
            { id: 3, name: '<PERSON>', email: '<EMAIL>', phone: '************', customer: 'DEF Ltd', position: 'Sales Director', notes: 'Main contact for sales discussions', createdAt: new Date().toISOString() }
        ];
        localStorage.setItem('contacts', JSON.stringify(contacts));
    }
    
    // Form elements
    const addContactBtn = document.getElementById('addContactBtn');
    const contactFormContainer = document.getElementById('contactFormContainer');
    const contactForm = document.getElementById('contactForm');
    const formTitle = document.getElementById('formTitle');
    const cancelContactBtn = document.getElementById('cancelContactBtn');
    const contactSearch = document.getElementById('contactSearch');
    const searchBtn = document.getElementById('searchBtn');
    const contactsTableBody = document.getElementById('contactsTableBody');
    const contactCustomerSelect = document.getElementById('contactCustomer');
    
    // Load customers for dropdown
    function loadCustomers() {
        const customers = JSON.parse(localStorage.getItem('customers') || '[]');
        contactCustomerSelect.innerHTML = '<option value="">Select Customer</option>';
        customers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.name;
            option.textContent = customer.name;
            contactCustomerSelect.appendChild(option);
        });
    }
    
    // Display contacts
    function displayContacts(contactsToShow = contacts) {
        contactsTableBody.innerHTML = '';
        contactsToShow.forEach(contact => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${contact.name}</td>
                <td><a href="mailto:${contact.email}">${contact.email}</a></td>
                <td><a href="tel:${contact.phone}">${contact.phone}</a></td>
                <td>${contact.customer || 'N/A'}</td>
                <td>${contact.position || 'N/A'}</td>
                <td>
                    <button class="btn-primary" onclick="editContact(${contact.id})">Edit</button>
                    <button class="btn-secondary" onclick="deleteContact(${contact.id})">Delete</button>
                </td>
            `;
            contactsTableBody.appendChild(row);
        });
    }
    
    // Show form
    function showForm(mode = 'add', contact = null) {
        contactFormContainer.style.display = 'block';
        loadCustomers();
        
        if (mode === 'add') {
            formTitle.textContent = 'Add New Contact';
            contactForm.reset();
            document.getElementById('contactId').value = '';
        } else {
            formTitle.textContent = 'Edit Contact';
            document.getElementById('contactId').value = contact.id;
            document.getElementById('contactName').value = contact.name;
            document.getElementById('contactEmail').value = contact.email;
            document.getElementById('contactPhone').value = contact.phone || '';
            document.getElementById('contactCustomer').value = contact.customer || '';
            document.getElementById('contactPosition').value = contact.position || '';
            document.getElementById('contactNotes').value = contact.notes || '';
        }
    }
    
    // Hide form
    function hideForm() {
        contactFormContainer.style.display = 'none';
        contactForm.reset();
    }
    
    // Add contact button
    if (addContactBtn) {
        addContactBtn.addEventListener('click', () => showForm('add'));
    }
    
    // Cancel button
    if (cancelContactBtn) {
        cancelContactBtn.addEventListener('click', hideForm);
    }
    
    // Form submission
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const contactId = document.getElementById('contactId').value;
            const contactData = {
                name: document.getElementById('contactName').value.trim(),
                email: document.getElementById('contactEmail').value.trim(),
                phone: document.getElementById('contactPhone').value.trim(),
                customer: document.getElementById('contactCustomer').value,
                position: document.getElementById('contactPosition').value.trim(),
                notes: document.getElementById('contactNotes').value.trim(),
                updatedAt: new Date().toISOString()
            };
            
            if (!contactData.name || !contactData.email) {
                alert('Name and email are required!');
                return;
            }
            
            if (!isValidEmail(contactData.email)) {
                alert('Please enter a valid email address!');
                return;
            }
            
            if (contactId) {
                // Edit existing contact
                const index = contacts.findIndex(c => c.id == contactId);
                if (index !== -1) {
                    contacts[index] = { ...contacts[index], ...contactData };
                }
            } else {
                // Add new contact
                contactData.id = Date.now();
                contactData.createdAt = new Date().toISOString();
                contacts.push(contactData);
            }
            
            localStorage.setItem('contacts', JSON.stringify(contacts));
            displayContacts();
            hideForm();
            showMessage('Contact saved successfully!', 'success');
        });
    }
    
    // Search functionality
    if (contactSearch && searchBtn) {
        searchBtn.addEventListener('click', function() {
            const searchTerm = contactSearch.value.toLowerCase();
            const filteredContacts = contacts.filter(contact => 
                contact.name.toLowerCase().includes(searchTerm) ||
                contact.email.toLowerCase().includes(searchTerm) ||
                contact.customer.toLowerCase().includes(searchTerm) ||
                contact.position.toLowerCase().includes(searchTerm)
            );
            displayContacts(filteredContacts);
        });
        
        contactSearch.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchBtn.click();
            }
        });
    }
    
    // Global functions for edit and delete
    window.editContact = function(id) {
        const contact = contacts.find(c => c.id == id);
        if (contact) {
            showForm('edit', contact);
        }
    };
    
    window.deleteContact = function(id) {
        if (confirm('Are you sure you want to delete this contact?')) {
            contacts = contacts.filter(c => c.id != id);
            localStorage.setItem('contacts', JSON.stringify(contacts));
            displayContacts();
            showMessage('Contact deleted successfully!', 'success');
        }
    };
    
    // Email validation
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    // Initial display
    displayContacts();
});

function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: bold;
        z-index: 1000;
        ${type === 'error' ? 'background-color: #e74c3c;' : 'background-color: #27ae60;'}
    `;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 3000);
}
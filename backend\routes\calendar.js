const express = require('express');
const {
  getEvents,
  getEvent,
  createEvent,
  updateEvent,
  deleteEvent,
  getUpcomingEvents
} = require('../controllers/calendarController');

const { protect } = require('../middleware/auth');
const asyncHandler = require('../middleware/asyncHandler');
const {
  validateUUID,
  validateRequired,
  validateOptional,
  validateEnum,
  validateDate,
  validatePagination,
  handleValidationErrors,
  body,
  query
} = require('../middleware/validation');

const router = express.Router();

// Validation rules
const validateCreateEvent = [
  validateRequired('title', 'Event title is required'),
  validateOptional('description'),
  validateDate('startDate', 'Start date is required'),
  validateDate('endDate', 'End date is required'),
  body('endDate').custom((value, { req }) => {
    if (new Date(value) < new Date(req.body.startDate)) {
      throw new Error('End date must be after start date');
    }
    return true;
  }),
  body('allDay')
    .optional()
    .isBoolean().withMessage('All day must be a boolean'),
  validateOptional('location'),
  validateEnum('type', ['meeting', 'call', 'task', 'event', 'reminder'], 'Invalid event type'),
  validateEnum('status', ['scheduled', 'completed', 'cancelled'], 'Invalid status'),
  validateEnum('priority', ['low', 'medium', 'high'], 'Invalid priority'),
  body('isRecurring')
    .optional()
    .isBoolean().withMessage('Is recurring must be a boolean'),
  validateOptional('recurringPattern'),
  body('reminder')
    .optional()
    .isInt({ min: 0 }).withMessage('Reminder must be a positive number (minutes)'),
  body('color')
    .optional()
    .matches(/^#[0-9A-Fa-f]{6}$/).withMessage('Color must be a valid hex color'),
  body('customerId')
    .optional()
    .isUUID().withMessage('Customer ID must be a valid UUID'),
  body('contactId')
    .optional()
    .isUUID().withMessage('Contact ID must be a valid UUID'),
  body('dealId')
    .optional()
    .isUUID().withMessage('Deal ID must be a valid UUID'),
  body('taskId')
    .optional()
    .isUUID().withMessage('Task ID must be a valid UUID'),
  body('assignedToId')
    .optional()
    .isUUID().withMessage('Assigned to must be a valid user ID')
];

const validateUpdateEvent = [
  validateOptional('title'),
  validateOptional('description'),
  body('startDate')
    .optional()
    .isISO8601().withMessage('Start date must be a valid date'),
  body('endDate')
    .optional()
    .isISO8601().withMessage('End date must be a valid date')
    .custom((value, { req }) => {
      if (value && req.body.startDate && new Date(value) < new Date(req.body.startDate)) {
        throw new Error('End date must be after start date');
      }
      return true;
    }),
  body('allDay')
    .optional()
    .isBoolean().withMessage('All day must be a boolean'),
  validateOptional('location'),
  validateEnum('type', ['meeting', 'call', 'task', 'event', 'reminder'], 'Invalid event type'),
  validateEnum('status', ['scheduled', 'completed', 'cancelled'], 'Invalid status'),
  validateEnum('priority', ['low', 'medium', 'high'], 'Invalid priority'),
  body('isRecurring')
    .optional()
    .isBoolean().withMessage('Is recurring must be a boolean'),
  validateOptional('recurringPattern'),
  body('reminder')
    .optional()
    .isInt({ min: 0 }).withMessage('Reminder must be a positive number (minutes)'),
  body('color')
    .optional()
    .matches(/^#[0-9A-Fa-f]{6}$/).withMessage('Color must be a valid hex color'),
  body('customerId')
    .optional()
    .isUUID().withMessage('Customer ID must be a valid UUID'),
  body('contactId')
    .optional()
    .isUUID().withMessage('Contact ID must be a valid UUID'),
  body('dealId')
    .optional()
    .isUUID().withMessage('Deal ID must be a valid UUID'),
  body('taskId')
    .optional()
    .isUUID().withMessage('Task ID must be a valid UUID'),
  body('assignedToId')
    .optional()
    .isUUID().withMessage('Assigned to must be a valid user ID')
];

const validateGetEvents = [
  ...validatePagination(),
  query('startDate')
    .optional()
    .isISO8601().withMessage('Start date must be a valid date'),
  query('endDate')
    .optional()
    .isISO8601().withMessage('End date must be a valid date'),
  query('type')
    .optional()
    .isIn(['meeting', 'call', 'task', 'event', 'reminder']).withMessage('Invalid event type'),
  query('status')
    .optional()
    .isIn(['scheduled', 'completed', 'cancelled']).withMessage('Invalid status'),
  query('assignedToId')
    .optional()
    .isUUID().withMessage('Assigned to ID must be a valid UUID'),
  query('customerId')
    .optional()
    .isUUID().withMessage('Customer ID must be a valid UUID')
];

// All routes require authentication
router.use(protect);

// Upcoming events route
router.get('/upcoming', asyncHandler(getUpcomingEvents));

// CRUD routes
router
  .route('/')
  .get(validateGetEvents, handleValidationErrors, asyncHandler(getEvents))
  .post(validateCreateEvent, handleValidationErrors, asyncHandler(createEvent));

router
  .route('/:id')
  .get(validateUUID('id'), handleValidationErrors, asyncHandler(getEvent))
  .put(validateUUID('id'), validateUpdateEvent, handleValidationErrors, asyncHandler(updateEvent))
  .delete(validateUUID('id'), handleValidationErrors, asyncHandler(deleteEvent));

module.exports = router;

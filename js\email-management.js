// Email Management functionality
document.addEventListener('DOMContentLoaded', function() {
    // Tab functionality
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            
            // Remove active class from all tabs
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));
            
            // Add active class to clicked tab
            this.classList.add('active');
            document.getElementById(tabName).classList.add('active');
            
            // Load content based on tab
            loadTabContent(tabName);
        });
    });
    
    // Load tab content
    function loadTabContent(tabName) {
        switch(tabName) {
            case 'compose':
                loadEmailStats();
                loadTemplates();
                break;
            case 'templates':
                loadTemplates();
                break;
            case 'history':
                loadEmailHistory();
                break;
            case 'bulk':
                loadBulkEmailForm();
                break;
        }
    }
    
    // Load email statistics
    function loadEmailStats() {
        const stats = emailSystem.getEmailStats();
        document.getElementById('totalEmails').textContent = stats.total;
        document.getElementById('openRate').textContent = stats.openRate + '%';
        document.getElementById('clickRate').textContent = stats.clickRate + '%';
        document.getElementById('totalTemplates').textContent = emailSystem.getAllTemplates().length;
    }
    
    // Load templates
    function loadTemplates() {
        const templates = emailSystem.getAllTemplates();
        const templateSelect = document.getElementById('emailTemplate');
        const bulkTemplateSelect = document.getElementById('bulkTemplate');
        const templatesGrid = document.getElementById('templatesGrid');
        
        // Clear existing options
        if (templateSelect) {
            templateSelect.innerHTML = '<option value="">Select a template...</option>';
        }
        if (bulkTemplateSelect) {
            bulkTemplateSelect.innerHTML = '<option value="">Select a template...</option>';
        }
        
        // Add template options
        templates.forEach(template => {
            if (templateSelect) {
                const option = document.createElement('option');
                option.value = template.id;
                option.textContent = template.name;
                templateSelect.appendChild(option);
            }
            if (bulkTemplateSelect) {
                const option = document.createElement('option');
                option.value = template.id;
                option.textContent = template.name;
                bulkTemplateSelect.appendChild(option);
            }
        });
        
        // Display templates grid
        if (templatesGrid) {
            templatesGrid.innerHTML = '';
            templates.forEach(template => {
                const templateCard = document.createElement('div');
                templateCard.className = 'template-card';
                templateCard.innerHTML = `
                    <h4>${template.name}</h4>
                    <p><strong>Category:</strong> ${template.category}</p>
                    <p><strong>Subject:</strong> ${template.subject}</p>
                    <div class="template-actions">
                        <button class="btn-primary" onclick="editTemplate(${template.id})">Edit</button>
                        <button class="btn-secondary" onclick="deleteTemplate(${template.id})">Delete</button>
                        <button class="btn-secondary" onclick="useTemplate(${template.id})">Use</button>
                    </div>
                `;
                templatesGrid.appendChild(templateCard);
            });
        }
    }
    
    // Load email history
    function loadEmailHistory() {
        const history = emailSystem.getEmailHistory();
        const historyList = document.getElementById('emailHistoryList');
        
        if (historyList) {
            historyList.innerHTML = '';
            history.forEach(email => {
                const emailItem = document.createElement('div');
                emailItem.className = 'email-item';
                emailItem.innerHTML = `
                    <div class="email-header">
                        <h4>${email.subject}</h4>
                        <span class="email-date">${new Date(email.sentAt).toLocaleDateString()}</span>
                    </div>
                    <div class="email-details">
                        <p><strong>To:</strong> ${email.to}</p>
                        <p><strong>From:</strong> ${email.fromName}</p>
                        <p><strong>Status:</strong> 
                            <span class="status-${email.opened ? 'opened' : 'sent'}">${email.opened ? 'Opened' : 'Sent'}</span>
                            ${email.clicked ? ' (Clicked)' : ''}
                        </p>
                    </div>
                    <div class="email-body">
                        <p>${email.body.substring(0, 100)}${email.body.length > 100 ? '...' : ''}</p>
                    </div>
                `;
                historyList.appendChild(emailItem);
            });
        }
    }
    
    // Load bulk email form
    function loadBulkEmailForm() {
        // This will be handled by the template loading
    }
    
    // Email form functionality
    const emailForm = document.getElementById('emailForm');
    if (emailForm) {
        emailForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const to = document.getElementById('emailTo').value;
            const subject = document.getElementById('emailSubject').value;
            const body = document.getElementById('emailBody').value;
            const relatedTo = document.getElementById('emailRelated').value;
            
            const result = emailSystem.sendEmail(to, subject, body, relatedTo);
            
            if (result.success) {
                showMessage('Email sent successfully!', 'success');
                emailForm.reset();
                loadEmailStats();
            } else {
                showMessage('Failed to send email', 'error');
            }
        });
    }
    
    // Template selection
    const emailTemplate = document.getElementById('emailTemplate');
    if (emailTemplate) {
        emailTemplate.addEventListener('change', function() {
            const templateId = this.value;
            if (templateId) {
                const template = emailSystem.getTemplate(templateId);
                if (template) {
                    document.getElementById('emailSubject').value = template.subject;
                    document.getElementById('emailBody').value = template.body;
                }
            }
        });
    }
    
    // Template form functionality
    const addTemplateBtn = document.getElementById('addTemplateBtn');
    const templateFormContainer = document.getElementById('templateFormContainer');
    const templateForm = document.getElementById('templateForm');
    const cancelTemplateBtn = document.getElementById('cancelTemplateBtn');
    
    if (addTemplateBtn) {
        addTemplateBtn.addEventListener('click', function() {
            showTemplateForm();
        });
    }
    
    if (cancelTemplateBtn) {
        cancelTemplateBtn.addEventListener('click', function() {
            hideTemplateForm();
        });
    }
    
    if (templateForm) {
        templateForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const templateId = document.getElementById('templateId').value;
            const templateData = {
                name: document.getElementById('templateName').value,
                category: document.getElementById('templateCategory').value,
                subject: document.getElementById('templateSubject').value,
                body: document.getElementById('templateBody').value
            };
            
            if (templateId) {
                // Edit existing template
                const result = emailSystem.updateTemplate(templateId, templateData);
                if (result.success) {
                    showMessage('Template updated successfully!', 'success');
                    hideTemplateForm();
                    loadTemplates();
                } else {
                    showMessage(result.message, 'error');
                }
            } else {
                // Create new template
                const result = emailSystem.createTemplate(templateData);
                if (result.success) {
                    showMessage('Template created successfully!', 'success');
                    hideTemplateForm();
                    loadTemplates();
                } else {
                    showMessage('Failed to create template', 'error');
                }
            }
        });
    }
    
    // Show template form
    function showTemplateForm(template = null) {
        templateFormContainer.style.display = 'block';
        
        if (template) {
            document.getElementById('templateId').value = template.id;
            document.getElementById('templateName').value = template.name;
            document.getElementById('templateCategory').value = template.category;
            document.getElementById('templateSubject').value = template.subject;
            document.getElementById('templateBody').value = template.body;
        } else {
            templateForm.reset();
            document.getElementById('templateId').value = '';
        }
    }
    
    // Hide template form
    function hideTemplateForm() {
        templateFormContainer.style.display = 'none';
        templateForm.reset();
    }
    
    // Bulk email form
    const bulkEmailForm = document.getElementById('bulkEmailForm');
    if (bulkEmailForm) {
        bulkEmailForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const templateId = document.getElementById('bulkTemplate').value;
            const recipients = document.getElementById('bulkRecipients').value;
            const variablesText = document.getElementById('bulkVariables').value;
            
            let variables = {};
            if (variablesText) {
                try {
                    variables = JSON.parse(variablesText);
                } catch (e) {
                    showMessage('Invalid JSON in variables field', 'error');
                    return;
                }
            }
            
            // Get recipients based on selection
            let recipientList = [];
            switch(recipients) {
                case 'all-customers':
                    const customers = JSON.parse(localStorage.getItem('customers') || '[]');
                    recipientList = customers.map(c => ({
                        name: c.name,
                        email: c.email,
                        relatedTo: 'customer',
                        relatedId: c.id
                    }));
                    break;
                case 'active-deals':
                    const deals = JSON.parse(localStorage.getItem('deals') || '[]');
                    const activeDeals = deals.filter(d => d.stage === 'active');
                    recipientList = activeDeals.map(d => ({
                        name: d.customerName,
                        email: d.customerEmail,
                        relatedTo: 'deal',
                        relatedId: d.id
                    }));
                    break;
                case 'recent-contacts':
                    const contacts = JSON.parse(localStorage.getItem('contacts') || '[]');
                    const recentContacts = contacts.slice(0, 10); // Last 10 contacts
                    recipientList = recentContacts.map(c => ({
                        name: c.name,
                        email: c.email,
                        relatedTo: 'contact',
                        relatedId: c.id
                    }));
                    break;
            }
            
            if (recipientList.length === 0) {
                showMessage('No recipients found for the selected option', 'error');
                return;
            }
            
            const result = emailSystem.sendBulkEmail(recipientList, templateId, variables);
            if (result.success) {
                showMessage(`Bulk email sent to ${recipientList.length} recipients!`, 'success');
                loadEmailStats();
            } else {
                showMessage(result.message, 'error');
            }
        });
    }
    
    // Global functions
    window.editTemplate = function(id) {
        const template = emailSystem.getTemplate(id);
        if (template) {
            showTemplateForm(template);
        }
    };
    
    window.deleteTemplate = function(id) {
        if (confirm('Are you sure you want to delete this template?')) {
            const result = emailSystem.deleteTemplate(id);
            if (result.success) {
                showMessage('Template deleted successfully!', 'success');
                loadTemplates();
            } else {
                showMessage('Failed to delete template', 'error');
            }
        }
    };
    
    window.useTemplate = function(id) {
        const template = emailSystem.getTemplate(id);
        if (template) {
            // Switch to compose tab and fill form
            document.querySelector('[data-tab="compose"]').click();
            document.getElementById('emailSubject').value = template.subject;
            document.getElementById('emailBody').value = template.body;
        }
    };
    
    // Initialize
    loadEmailStats();
    loadTemplates();
});

function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: bold;
        z-index: 1000;
        ${type === 'error' ? 'background-color: #e74c3c;' : 'background-color: #27ae60;'}
    `;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 3000);
} 
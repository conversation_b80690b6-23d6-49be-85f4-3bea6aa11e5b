// User Management functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check if user has permission to access this page
    if (!auth.hasPermission('users')) {
        window.location.href = 'dashboard.html';
        return;
    }
    
    // Form elements
    const addUserBtn = document.getElementById('addUserBtn');
    const userFormContainer = document.getElementById('userFormContainer');
    const userForm = document.getElementById('userForm');
    const formTitle = document.getElementById('formTitle');
    const cancelUserBtn = document.getElementById('cancelUserBtn');
    const userSearch = document.getElementById('userSearch');
    const searchBtn = document.getElementById('searchBtn');
    const usersTableBody = document.getElementById('usersTableBody');
    
    // Display users
    function displayUsers(usersToShow = null) {
        const users = usersToShow || auth.getAllUsers();
        usersTableBody.innerHTML = '';
        
        users.forEach(user => {
            const row = document.createElement('tr');
            const roleInfo = auth.getUserRoleInfo(user.role);
            const isCurrentUser = user.id === auth.currentUser.id;
            
            row.innerHTML = `
                <td>${user.fullName}</td>
                <td>${user.username}</td>
                <td>${user.email}</td>
                <td><span class="role-badge" style="background-color: ${roleInfo.color}">${roleInfo.name}</span></td>
                <td>${user.department || 'N/A'}</td>
                <td><span class="status-active">Active</span></td>
                <td>
                    ${!isCurrentUser ? `
                        <button class="btn-primary" onclick="editUser(${user.id})">Edit</button>
                        <button class="btn-secondary" onclick="deleteUser(${user.id})">Delete</button>
                    ` : '<span class="current-user">Current User</span>'}
                </td>
            `;
            usersTableBody.appendChild(row);
        });
    }
    
    // Show form
    function showForm(mode = 'add', user = null) {
        userFormContainer.style.display = 'block';
        
        if (mode === 'add') {
            formTitle.textContent = 'Add New User';
            userForm.reset();
            document.getElementById('userId').value = '';
            document.getElementById('userPassword').required = true;
            document.getElementById('userConfirmPassword').required = true;
        } else {
            formTitle.textContent = 'Edit User';
            document.getElementById('userId').value = user.id;
            document.getElementById('userFullName').value = user.fullName;
            document.getElementById('userEmail').value = user.email;
            document.getElementById('userUsername').value = user.username;
            document.getElementById('userRoleSelect').value = user.role;
            document.getElementById('userDepartment').value = user.department || '';
            document.getElementById('userNotes').value = user.notes || '';
            document.getElementById('userPassword').required = false;
            document.getElementById('userConfirmPassword').required = false;
        }
    }
    
    // Hide form
    function hideForm() {
        userFormContainer.style.display = 'none';
        userForm.reset();
    }
    
    // Add user button
    if (addUserBtn) {
        addUserBtn.addEventListener('click', () => showForm('add'));
    }
    
    // Cancel button
    if (cancelUserBtn) {
        cancelUserBtn.addEventListener('click', hideForm);
    }
    
    // Form submission
    if (userForm) {
        userForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const userId = document.getElementById('userId').value;
            const userData = {
                fullName: document.getElementById('userFullName').value.trim(),
                email: document.getElementById('userEmail').value.trim(),
                username: document.getElementById('userUsername').value.trim(),
                role: document.getElementById('userRoleSelect').value,
                department: document.getElementById('userDepartment').value.trim(),
                notes: document.getElementById('userNotes').value.trim()
            };
            
            if (!userData.fullName || !userData.email || !userData.username) {
                alert('Full name, email, and username are required!');
                return;
            }
            
            if (!isValidEmail(userData.email)) {
                alert('Please enter a valid email address!');
                return;
            }
            
            if (userId) {
                // Edit existing user
                const password = document.getElementById('userPassword').value;
                if (password) {
                    if (password !== document.getElementById('userConfirmPassword').value) {
                        alert('Passwords do not match!');
                        return;
                    }
                    userData.password = password;
                }
                
                const result = auth.updateUser(userId, userData);
                if (result.success) {
                    displayUsers();
                    hideForm();
                    showMessage('User updated successfully!', 'success');
                } else {
                    showMessage(result.message, 'error');
                }
            } else {
                // Add new user
                const password = document.getElementById('userPassword').value;
                const confirmPassword = document.getElementById('userConfirmPassword').value;
                
                if (!password) {
                    alert('Password is required for new users!');
                    return;
                }
                
                if (password !== confirmPassword) {
                    alert('Passwords do not match!');
                    return;
                }
                
                userData.password = password;
                const result = auth.createUser(userData);
                
                if (result.success) {
                    displayUsers();
                    hideForm();
                    showMessage('User created successfully!', 'success');
                } else {
                    showMessage(result.message, 'error');
                }
            }
        });
    }
    
    // Search functionality
    if (userSearch && searchBtn) {
        searchBtn.addEventListener('click', function() {
            const searchTerm = userSearch.value.toLowerCase();
            const users = auth.getAllUsers();
            const filteredUsers = users.filter(user => 
                user.fullName.toLowerCase().includes(searchTerm) ||
                user.username.toLowerCase().includes(searchTerm) ||
                user.email.toLowerCase().includes(searchTerm) ||
                user.department.toLowerCase().includes(searchTerm)
            );
            displayUsers(filteredUsers);
        });
        
        userSearch.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchBtn.click();
            }
        });
    }
    
    // Global functions
    window.editUser = function(id) {
        const users = auth.getAllUsers();
        const user = users.find(u => u.id == id);
        if (user) {
            showForm('edit', user);
        }
    };
    
    window.deleteUser = function(id) {
        if (id === auth.currentUser.id) {
            alert('You cannot delete your own account!');
            return;
        }
        
        if (confirm('Are you sure you want to delete this user?')) {
            const result = auth.deleteUser(id);
            if (result.success) {
                displayUsers();
                showMessage('User deleted successfully!', 'success');
            } else {
                showMessage(result.message, 'error');
            }
        }
    };
    
    // Email validation
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    // Initial display
    displayUsers();
});

function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: bold;
        z-index: 1000;
        ${type === 'error' ? 'background-color: #e74c3c;' : 'background-color: #27ae60;'}
    `;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 3000);
} 
// Reports functionality
document.addEventListener('DOMContentLoaded', function() {
    // Logout functionality
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function() {
            auth.logout();
        });
    }
    
    // Initialize charts if Chart.js is available
    if (typeof Chart !== 'undefined') {
        // Revenue chart
        const revenueCtx = document.getElementById('revenueChart');
        if (revenueCtx) {
            // Generate sample revenue data for the last 6 months
            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
            const revenueData = months.map(() => Math.floor(Math.random() * 50000) + 10000);
            
            new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: months,
                    datasets: [{
                        label: 'Revenue',
                        data: revenueData,
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: true,
                            text: 'Revenue Trend'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        }
    }
    
    // Load real data from localStorage
    const customers = JSON.parse(localStorage.getItem('customers') || '[]');
    const deals = JSON.parse(localStorage.getItem('deals') || '[]');
    const tasks = JSON.parse(localStorage.getItem('tasks') || '[]');
    const contacts = JSON.parse(localStorage.getItem('contacts') || '[]');
    
    // Calculate real statistics
    const totalRevenue = deals.reduce((sum, deal) => {
        if (deal.stage === 'Closed Won') {
            return sum + (deal.value || 0);
        }
        return sum;
    }, 0);
    
    const totalDeals = deals.length;
    const activeDeals = deals.filter(deal => deal.stage !== 'Closed Won' && deal.stage !== 'Closed Lost').length;
    const conversionRate = totalDeals > 0 ? ((deals.filter(deal => deal.stage === 'Closed Won').length / totalDeals) * 100).toFixed(1) : 0;
    const avgDealSize = totalDeals > 0 ? (totalRevenue / deals.filter(deal => deal.stage === 'Closed Won').length).toFixed(0) : 0;
    
    // Update summary cards with real data
    const totalRevenueElement = document.getElementById('totalRevenue');
    const newCustomersElement = document.getElementById('newCustomers');
    const dealsClosedElement = document.getElementById('dealsClosed');
    const tasksCompletedElement = document.getElementById('tasksCompleted');
    
    if (totalRevenueElement) totalRevenueElement.textContent = `$${totalRevenue.toLocaleString()}`;
    if (newCustomersElement) newCustomersElement.textContent = customers.length;
    if (dealsClosedElement) dealsClosedElement.textContent = deals.filter(deal => deal.stage === 'Closed Won').length;
    if (tasksCompletedElement) tasksCompletedElement.textContent = tasks.filter(task => task.completed).length;
    
    // Generate deal stage breakdown
    const dealStages = {};
    deals.forEach(deal => {
        dealStages[deal.stage] = (dealStages[deal.stage] || 0) + 1;
    });
    
    // Create deal stage chart if canvas exists
    const dealStageCtx = document.getElementById('dealStageChart');
    if (dealStageCtx && typeof Chart !== 'undefined') {
        new Chart(dealStageCtx, {
            type: 'doughnut',
            data: {
                labels: Object.keys(dealStages),
                datasets: [{
                    data: Object.values(dealStages),
                    backgroundColor: [
                        '#e74c3c', '#f39c12', '#f1c40f', '#27ae60', '#3498db', '#9b59b6'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    title: {
                        display: true,
                        text: 'Deals by Stage'
                    }
                }
            }
        });
    }
    
    // Export functionality
    const exportBtn = document.getElementById('exportData');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            const data = {
                customers: customers,
                deals: deals,
                tasks: tasks,
                contacts: contacts,
                exportDate: new Date().toISOString()
            };
            
            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `crm-data-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);
            
            showMessage('Data exported successfully!', 'success');
        });
    }
    
    // Filter functionality
    const filterForm = document.getElementById('reportsFilter');
    if (filterForm) {
        filterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            if (startDate && endDate) {
                const filteredDeals = deals.filter(deal => {
                    const dealDate = new Date(deal.createdAt);
                    const start = new Date(startDate);
                    const end = new Date(endDate);
                    return dealDate >= start && dealDate <= end;
                });
                
                const filteredRevenue = filteredDeals.reduce((sum, deal) => {
                    if (deal.stage === 'Closed Won') {
                        return sum + (deal.value || 0);
                    }
                    return sum;
                }, 0);
                
                if (totalRevenueElement) totalRevenueElement.textContent = `$${filteredRevenue.toLocaleString()}`;
                if (totalDealsElement) totalDealsElement.textContent = filteredDeals.length;
                
                showMessage('Report filtered successfully!', 'success');
            } else {
                showMessage('Please select both start and end dates', 'error');
            }
        });
    }
});

function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: bold;
        z-index: 1000;
        ${type === 'error' ? 'background-color: #e74c3c;' : 'background-color: #27ae60;'}
    `;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 3000);
}
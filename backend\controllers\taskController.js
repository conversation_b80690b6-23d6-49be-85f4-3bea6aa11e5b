const { Task, Customer, Contact, Deal, User, Activity } = require('../models');
const { Op } = require('sequelize');

// @desc    Get all tasks
// @route   GET /api/tasks
// @access  Private
exports.getTasks = async (req, res, next) => {
  try {
    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 25;
    const offset = (page - 1) * limit;

    // Filtering
    const where = {};
    
    if (req.query.status) {
      where.status = req.query.status;
    }
    
    if (req.query.priority) {
      where.priority = req.query.priority;
    }
    
    if (req.query.type) {
      where.type = req.query.type;
    }
    
    if (req.query.customerId) {
      where.customerId = req.query.customerId;
    }
    
    if (req.query.assignedToId) {
      where.assignedToId = req.query.assignedToId;
    }
    
    if (req.query.dealId) {
      where.dealId = req.query.dealId;
    }
    
    // Date filters
    if (req.query.dueDateFrom) {
      where.dueDate = { [Op.gte]: new Date(req.query.dueDateFrom) };
    }
    
    if (req.query.dueDateTo) {
      where.dueDate = { ...where.dueDate, [Op.lte]: new Date(req.query.dueDateTo) };
    }
    
    if (req.query.overdue === 'true') {
      where.dueDate = { [Op.lt]: new Date() };
      where.status = { [Op.ne]: 'completed' };
    }
    
    if (req.query.search) {
      where[Op.or] = [
        { title: { [Op.iLike]: `%${req.query.search}%` } },
        { description: { [Op.iLike]: `%${req.query.search}%` } }
      ];
    }

    // Sorting
    const order = [];
    if (req.query.sortBy) {
      const sortParts = req.query.sortBy.split(':');
      order.push([sortParts[0], sortParts[1] ? sortParts[1].toUpperCase() : 'ASC']);
    } else {
      order.push(['dueDate', 'ASC']);
    }

    const { count, rows } = await Task.findAndCountAll({
      where,
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'companyName']
        },
        {
          model: Contact,
          as: 'contact',
          attributes: ['id', 'firstName', 'lastName']
        },
        {
          model: Deal,
          as: 'deal',
          attributes: ['id', 'title']
        },
        {
          model: User,
          as: 'assignedTo',
          attributes: ['id', 'firstName', 'lastName']
        }
      ],
      order,
      limit,
      offset,
      distinct: true
    });

    res.status(200).json({
      success: true,
      count,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(count / limit)
      },
      data: rows
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single task
// @route   GET /api/tasks/:id
// @access  Private
exports.getTask = async (req, res, next) => {
  try {
    const task = await Task.findByPk(req.params.id, {
      include: [
        {
          model: Customer,
          as: 'customer'
        },
        {
          model: Contact,
          as: 'contact'
        },
        {
          model: Deal,
          as: 'deal'
        },
        {
          model: User,
          as: 'assignedTo',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'firstName', 'lastName']
        }
      ]
    });

    if (!task) {
      return res.status(404).json({
        success: false,
        error: 'Task not found'
      });
    }

    res.status(200).json({
      success: true,
      data: task
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create new task
// @route   POST /api/tasks
// @access  Private
exports.createTask = async (req, res, next) => {
  try {
    req.body.createdById = req.user.id;
    
    if (!req.body.assignedToId) {
      req.body.assignedToId = req.user.id;
    }

    const task = await Task.create(req.body);

    // Log activity
    await Activity.create({
      type: 'task_created',
      subject: 'New task created',
      description: `Task "${task.title}" was created`,
      entityType: 'task',
      entityId: task.id,
      customerId: task.customerId,
      dealId: task.dealId,
      performedById: req.user.id
    });

    // Load with associations
    const taskWithAssociations = await Task.findByPk(task.id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'companyName']
        },
        {
          model: User,
          as: 'assignedTo',
          attributes: ['id', 'firstName', 'lastName']
        }
      ]
    });

    res.status(201).json({
      success: true,
      data: taskWithAssociations
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update task
// @route   PUT /api/tasks/:id
// @access  Private
exports.updateTask = async (req, res, next) => {
  try {
    let task = await Task.findByPk(req.params.id);

    if (!task) {
      return res.status(404).json({
        success: false,
        error: 'Task not found'
      });
    }

    // Track status changes
    const oldStatus = task.status;
    
    task = await task.update(req.body);

    // Log status change activity
    if (oldStatus !== task.status) {
      await Activity.create({
        type: 'status_change',
        subject: 'Task status updated',
        description: `Task status changed from ${oldStatus} to ${task.status}`,
        entityType: 'task',
        entityId: task.id,
        customerId: task.customerId,
        dealId: task.dealId,
        performedById: req.user.id
      });
    }

    res.status(200).json({
      success: true,
      data: task
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete task
// @route   DELETE /api/tasks/:id
// @access  Private (Admin/Manager)
exports.deleteTask = async (req, res, next) => {
  try {
    const task = await Task.findByPk(req.params.id);

    if (!task) {
      return res.status(404).json({
        success: false,
        error: 'Task not found'
      });
    }

    await task.destroy();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Mark task as complete
// @route   PUT /api/tasks/:id/complete
// @access  Private
exports.completeTask = async (req, res, next) => {
  try {
    let task = await Task.findByPk(req.params.id);

    if (!task) {
      return res.status(404).json({
        success: false,
        error: 'Task not found'
      });
    }

    task = await task.update({ 
      status: 'completed',
      completedAt: new Date()
    });

    // Log completion activity
    await Activity.create({
      type: 'task_completed',
      subject: 'Task completed',
      description: `Task "${task.title}" was marked as complete`,
      entityType: 'task',
      entityId: task.id,
      customerId: task.customerId,
      dealId: task.dealId,
      performedById: req.user.id
    });

    res.status(200).json({
      success: true,
      data: task
    });
  } catch (error) {
    next(error);
  }
};

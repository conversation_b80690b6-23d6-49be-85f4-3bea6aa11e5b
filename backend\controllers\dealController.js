const { Deal, Customer, Contact, User, Task, Activity, sequelize } = require('../models');
const { Op } = require('sequelize');

// @desc    Get all deals
// @route   GET /api/deals
// @access  Private
exports.getDeals = async (req, res, next) => {
  try {
    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 25;
    const offset = (page - 1) * limit;

    // Filtering
    const where = {};
    
    if (req.query.stage) {
      where.stage = req.query.stage;
    }
    
    if (req.query.status) {
      where.status = req.query.status;
    }
    
    if (req.query.customerId) {
      where.customerId = req.query.customerId;
    }
    
    if (req.query.assignedToId) {
      where.assignedToId = req.query.assignedToId;
    }
    
    if (req.query.minValue) {
      where.value = { [Op.gte]: parseFloat(req.query.minValue) };
    }
    
    if (req.query.maxValue) {
      where.value = { ...where.value, [Op.lte]: parseFloat(req.query.maxValue) };
    }
    
    if (req.query.search) {
      where[Op.or] = [
        { title: { [Op.iLike]: `%${req.query.search}%` } },
        { description: { [Op.iLike]: `%${req.query.search}%` } }
      ];
    }

    // Sorting
    const order = [];
    if (req.query.sortBy) {
      const sortParts = req.query.sortBy.split(':');
      order.push([sortParts[0], sortParts[1] ? sortParts[1].toUpperCase() : 'ASC']);
    } else {
      order.push(['createdAt', 'DESC']);
    }

    const { count, rows } = await Deal.findAndCountAll({
      where,
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'companyName']
        },
        {
          model: Contact,
          as: 'contact',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'assignedTo',
          attributes: ['id', 'firstName', 'lastName']
        }
      ],
      order,
      limit,
      offset,
      distinct: true
    });

    res.status(200).json({
      success: true,
      count,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(count / limit)
      },
      data: rows
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single deal
// @route   GET /api/deals/:id
// @access  Private
exports.getDeal = async (req, res, next) => {
  try {
    const deal = await Deal.findByPk(req.params.id, {
      include: [
        {
          model: Customer,
          as: 'customer'
        },
        {
          model: Contact,
          as: 'contact'
        },
        {
          model: User,
          as: 'assignedTo',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'createdBy',
          attributes: ['id', 'firstName', 'lastName']
        },
        {
          model: Task,
          as: 'tasks',
          where: { status: { [Op.ne]: 'completed' } },
          required: false,
          limit: 5,
          order: [['dueDate', 'ASC']]
        },
        {
          model: Activity,
          as: 'activities',
          limit: 10,
          order: [['activityDate', 'DESC']],
          include: [
            {
              model: User,
              as: 'performedBy',
              attributes: ['id', 'firstName', 'lastName']
            }
          ]
        }
      ]
    });

    if (!deal) {
      return res.status(404).json({
        success: false,
        error: 'Deal not found'
      });
    }

    res.status(200).json({
      success: true,
      data: deal
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create new deal
// @route   POST /api/deals
// @access  Private
exports.createDeal = async (req, res, next) => {
  try {
    req.body.createdById = req.user.id;
    
    if (!req.body.assignedToId) {
      req.body.assignedToId = req.user.id;
    }

    const deal = await Deal.create(req.body);

    // Log activity
    await Activity.create({
      type: 'deal_created',
      subject: 'New deal created',
      description: `Deal ${deal.title} was created with value $${deal.value}`,
      entityType: 'deal',
      entityId: deal.id,
      customerId: deal.customerId,
      dealId: deal.id,
      performedById: req.user.id
    });

    // Load with associations
    const dealWithAssociations = await Deal.findByPk(deal.id, {
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'companyName']
        },
        {
          model: Contact,
          as: 'contact',
          attributes: ['id', 'firstName', 'lastName']
        },
        {
          model: User,
          as: 'assignedTo',
          attributes: ['id', 'firstName', 'lastName']
        }
      ]
    });

    res.status(201).json({
      success: true,
      data: dealWithAssociations
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update deal
// @route   PUT /api/deals/:id
// @access  Private
exports.updateDeal = async (req, res, next) => {
  try {
    let deal = await Deal.findByPk(req.params.id);

    if (!deal) {
      return res.status(404).json({
        success: false,
        error: 'Deal not found'
      });
    }

    // Track stage changes
    const oldStage = deal.stage;
    
    deal = await deal.update(req.body);

    // Log stage change activity
    if (oldStage !== deal.stage) {
      await Activity.create({
        type: 'stage_change',
        subject: 'Deal stage updated',
        description: `Deal stage changed from ${oldStage} to ${deal.stage}`,
        entityType: 'deal',
        entityId: deal.id,
        customerId: deal.customerId,
        dealId: deal.id,
        performedById: req.user.id
      });
    }

    res.status(200).json({
      success: true,
      data: deal
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete deal
// @route   DELETE /api/deals/:id
// @access  Private (Admin/Manager)
exports.deleteDeal = async (req, res, next) => {
  try {
    const deal = await Deal.findByPk(req.params.id);

    if (!deal) {
      return res.status(404).json({
        success: false,
        error: 'Deal not found'
      });
    }

    await deal.destroy();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get deal statistics
// @route   GET /api/deals/stats
// @access  Private
exports.getDealStats = async (req, res, next) => {
  try {
    const stats = await Deal.findAll({
      attributes: [
        'stage',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
        [sequelize.fn('SUM', sequelize.col('value')), 'totalValue']
      ],
      group: ['stage']
    });

    const totalDeals = await Deal.count();
    const totalValue = await Deal.sum('value');
    const avgDealValue = await Deal.aggregate('value', 'AVG');

    res.status(200).json({
      success: true,
      data: {
        byStage: stats,
        summary: {
          totalDeals,
          totalValue,
          avgDealValue
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM System - Contacts</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/modern-ui.css">
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1>CRM System</h1>
            </div>
            <div class="user-info">
                <span id="username">User</span>
                <span id="userRole" class="user-role">Role</span>
                <button id="logoutBtn">Logout</button>
            </div>
        </header>
        
        <nav>
            <ul>
                <li><a href="dashboard.html">Dashboard</a></li>
                <li><a href="customers.html">Customers</a></li>
                <li><a href="contacts.html" class="active">Contacts</a></li>
                <li><a href="deals.html">Deals</a></li>
                <li><a href="tasks.html">Tasks</a></li>
                <li><a href="calendar.html">Calendar</a></li>
                <li><a href="reports.html">Reports</a></li>
                <li><a href="users.html" class="admin-only">Users</a></li>
                <li><a href="email.html">Email</a></li>
            </ul>
        </nav>
        
        <main>
            <h2>Contact Management</h2>
            
            <div class="actions-bar">
                <button id="addContactBtn" class="btn-primary">Add New Contact</button>
                <div class="search-box">
                    <input type="text" id="contactSearch" placeholder="Search contacts...">
                    <button id="searchBtn">Search</button>
                </div>
            </div>
            
            <div class="contact-form-container" id="contactFormContainer" style="display: none;">
                <h3 id="formTitle">Add New Contact</h3>
                <form id="contactForm">
                    <input type="hidden" id="contactId">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="contactName">Name</label>
                            <input type="text" id="contactName" name="contactName" required>
                        </div>
                        <div class="form-group">
                            <label for="contactEmail">Email</label>
                            <input type="email" id="contactEmail" name="contactEmail" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="contactPhone">Phone</label>
                            <input type="text" id="contactPhone" name="contactPhone">
                        </div>
                        <div class="form-group">
                            <label for="contactCustomer">Customer</label>
                            <select id="contactCustomer" name="contactCustomer">
                                <option value="">Select Customer</option>
                                <!-- Customer options will be populated here -->
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="contactPosition">Position</label>
                        <input type="text" id="contactPosition" name="contactPosition">
                    </div>
                    <div class="form-group">
                        <label for="contactNotes">Notes</label>
                        <textarea id="contactNotes" name="contactNotes" rows="3"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">Save Contact</button>
                        <button type="button" id="cancelContactBtn" class="btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
            
            <div class="table-container">
                <table id="contactsTable">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Customer</th>
                            <th>Position</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="contactsTableBody">
                        <!-- Contact rows will be populated here -->
                    </tbody>
                </table>
            </div>
        </main>
    </div>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/contacts.js"></script>
</body>
</html>
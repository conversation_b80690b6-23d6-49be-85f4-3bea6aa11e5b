<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM System - Email Management</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/modern-ui.css">
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">
                <h1>CRM System</h1>
            </div>
            <div class="user-info">
                <span id="username">User</span>
                <span id="userRole" class="user-role">Role</span>
                <button id="logoutBtn">Logout</button>
            </div>
        </header>
        
        <nav>
            <ul>
                <li><a href="dashboard.html">Dashboard</a></li>
                <li><a href="customers.html">Customers</a></li>
                <li><a href="contacts.html">Contacts</a></li>
                <li><a href="deals.html">Deals</a></li>
                <li><a href="tasks.html">Tasks</a></li>
                <li><a href="calendar.html">Calendar</a></li>
                <li><a href="reports.html">Reports</a></li>
                <li><a href="users.html" class="admin-only">Users</a></li>
                <li><a href="email.html" class="active">Email</a></li>
            </ul>
        </nav>
        
        <main>
            <h2>Email Management</h2>
            
            <div class="email-stats">
                <div class="stat-card">
                    <h3>Total Emails</h3>
                    <p id="totalEmails">0</p>
                </div>
                <div class="stat-card">
                    <h3>Open Rate</h3>
                    <p id="openRate">0%</p>
                </div>
                <div class="stat-card">
                    <h3>Click Rate</h3>
                    <p id="clickRate">0%</p>
                </div>
                <div class="stat-card">
                    <h3>Templates</h3>
                    <p id="totalTemplates">0</p>
                </div>
            </div>
            
            <div class="email-tabs">
                <button class="tab-btn active" data-tab="compose">Compose Email</button>
                <button class="tab-btn" data-tab="templates">Email Templates</button>
                <button class="tab-btn" data-tab="history">Email History</button>
                <button class="tab-btn" data-tab="bulk">Bulk Email</button>
            </div>
            
            <!-- Compose Email Tab -->
            <div class="tab-content active" id="compose">
                <div class="email-compose">
                    <h3>Compose New Email</h3>
                    <form id="emailForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="emailTo">To</label>
                                <input type="email" id="emailTo" name="emailTo" required>
                            </div>
                            <div class="form-group">
                                <label for="emailSubject">Subject</label>
                                <input type="text" id="emailSubject" name="emailSubject" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="emailBody">Message</label>
                            <textarea id="emailBody" name="emailBody" rows="10" required></textarea>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="emailTemplate">Use Template</label>
                                <select id="emailTemplate" name="emailTemplate">
                                    <option value="">Select a template...</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="emailRelated">Related To</label>
                                <select id="emailRelated" name="emailRelated">
                                    <option value="">None</option>
                                    <option value="customer">Customer</option>
                                    <option value="deal">Deal</option>
                                    <option value="contact">Contact</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn-primary">Send Email</button>
                            <button type="button" id="saveDraftBtn" class="btn-secondary">Save Draft</button>
                            <button type="button" id="previewBtn" class="btn-secondary">Preview</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Templates Tab -->
            <div class="tab-content" id="templates">
                <div class="templates-section">
                    <div class="actions-bar">
                        <button id="addTemplateBtn" class="btn-primary">Add Template</button>
                    </div>
                    
                    <div class="template-form-container" id="templateFormContainer" style="display: none;">
                        <h3 id="templateFormTitle">Add Email Template</h3>
                        <form id="templateForm">
                            <input type="hidden" id="templateId">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="templateName">Template Name</label>
                                    <input type="text" id="templateName" name="templateName" required>
                                </div>
                                <div class="form-group">
                                    <label for="templateCategory">Category</label>
                                    <select id="templateCategory" name="templateCategory" required>
                                        <option value="welcome">Welcome</option>
                                        <option value="follow-up">Follow-up</option>
                                        <option value="reminder">Reminder</option>
                                        <option value="custom">Custom</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="templateSubject">Subject</label>
                                <input type="text" id="templateSubject" name="templateSubject" required>
                            </div>
                            <div class="form-group">
                                <label for="templateBody">Body</label>
                                <textarea id="templateBody" name="templateBody" rows="10" required></textarea>
                                <small>Use {{variableName}} for dynamic content</small>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn-primary">Save Template</button>
                                <button type="button" id="cancelTemplateBtn" class="btn-secondary">Cancel</button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="templates-grid" id="templatesGrid">
                        <!-- Templates will be displayed here -->
                    </div>
                </div>
            </div>
            
            <!-- History Tab -->
            <div class="tab-content" id="history">
                <div class="email-history">
                    <div class="actions-bar">
                        <div class="search-box">
                            <input type="text" id="emailSearch" placeholder="Search emails...">
                            <button id="emailSearchBtn">Search</button>
                        </div>
                        <select id="emailFilter">
                            <option value="all">All Emails</option>
                            <option value="sent">Sent</option>
                            <option value="opened">Opened</option>
                            <option value="clicked">Clicked</option>
                        </select>
                    </div>
                    
                    <div class="email-history-list" id="emailHistoryList">
                        <!-- Email history will be displayed here -->
                    </div>
                </div>
            </div>
            
            <!-- Bulk Email Tab -->
            <div class="tab-content" id="bulk">
                <div class="bulk-email">
                    <h3>Bulk Email Campaign</h3>
                    <form id="bulkEmailForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="bulkTemplate">Template</label>
                                <select id="bulkTemplate" name="bulkTemplate" required>
                                    <option value="">Select a template...</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="bulkRecipients">Recipients</label>
                                <select id="bulkRecipients" name="bulkRecipients" required>
                                    <option value="">Select recipients...</option>
                                    <option value="all-customers">All Customers</option>
                                    <option value="active-deals">Active Deals</option>
                                    <option value="recent-contacts">Recent Contacts</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="bulkVariables">Additional Variables (JSON)</label>
                            <textarea id="bulkVariables" name="bulkVariables" rows="3" placeholder='{"userName": "John Doe", "companyName": "ACME Corp"}'></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn-primary">Send Bulk Email</button>
                            <button type="button" id="previewBulkBtn" class="btn-secondary">Preview</button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>
    
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/email.js"></script>
    <script src="js/email-management.js"></script>
</body>
</html> 
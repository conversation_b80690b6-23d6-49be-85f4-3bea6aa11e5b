<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Test</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <h1>Login Test Page</h1>
        
        <div class="test-section">
            <h3>Test Login</h3>
            <form id="testLoginForm">
                <div class="form-group">
                    <label for="testUsername">Username</label>
                    <input type="text" id="testUsername" value="admin">
                </div>
                <div class="form-group">
                    <label for="testPassword">Password</label>
                    <input type="password" id="testPassword" value="admin123">
                </div>
                <button type="submit">Test Login</button>
            </form>
            <div id="testResult"></div>
        </div>
        
        <div class="test-section">
            <h3>Available Users</h3>
            <div id="userList"></div>
        </div>
        
        <div class="test-section">
            <h3>Auth System Status</h3>
            <div id="authStatus"></div>
        </div>
        
        <div class="test-section">
            <h3>Actions</h3>
            <button onclick="createTestUsers()">Create Test Users</button>
            <button onclick="clearAllData()">Clear All Data</button>
            <button onclick="goToLogin()">Go to Login Page</button>
        </div>
    </div>
    
    <script src="js/auth.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Test auth system
            const authStatus = document.getElementById('authStatus');
            if (typeof auth !== 'undefined') {
                authStatus.innerHTML = '<p style="color: green;">✅ Auth system loaded successfully</p>';
            } else {
                authStatus.innerHTML = '<p style="color: red;">❌ Auth system not loaded</p>';
            }
            
            // Show available users
            updateUserList();
            
            // Test login form
            const testForm = document.getElementById('testLoginForm');
            testForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const username = document.getElementById('testUsername').value;
                const password = document.getElementById('testPassword').value;
                const resultDiv = document.getElementById('testResult');
                
                if (typeof auth !== 'undefined') {
                    const loginResult = auth.login(username, password);
                    if (loginResult) {
                        resultDiv.innerHTML = '<p style="color: green;">✅ Login successful! User: ' + auth.currentUser.fullName + ' (Role: ' + auth.currentUser.role + ')</p>';
                    } else {
                        resultDiv.innerHTML = '<p style="color: red;">❌ Login failed</p>';
                    }
                } else {
                    resultDiv.innerHTML = '<p style="color: red;">❌ Auth system not available</p>';
                }
            });
        });
        
        function updateUserList() {
            const userList = document.getElementById('userList');
            const users = JSON.parse(localStorage.getItem('users') || '[]');
            
            if (users.length === 0) {
                userList.innerHTML = '<p>No users found</p>';
            } else {
                let html = '<ul>';
                users.forEach(user => {
                    html += `<li><strong>${user.username}</strong> (${user.role}) - ${user.fullName}</li>`;
                });
                html += '</ul>';
                userList.innerHTML = html;
            }
        }
        
        function createTestUsers() {
            const users = [
                {
                    id: 1,
                    fullName: 'Demo Administrator',
                    email: '<EMAIL>',
                    username: 'admin',
                    password: btoa('admin123'),
                    department: 'IT',
                    role: 'admin',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 2,
                    fullName: 'Demo Manager',
                    email: '<EMAIL>',
                    username: 'manager',
                    password: btoa('manager123'),
                    department: 'Sales',
                    role: 'manager',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 3,
                    fullName: 'Demo User',
                    email: '<EMAIL>',
                    username: 'user',
                    password: btoa('user123'),
                    department: 'Marketing',
                    role: 'user',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 4,
                    fullName: 'Demo Viewer',
                    email: '<EMAIL>',
                    username: 'viewer',
                    password: btoa('viewer123'),
                    department: 'Support',
                    role: 'viewer',
                    createdAt: new Date().toISOString()
                }
            ];
            
            localStorage.setItem('users', JSON.stringify(users));
            updateUserList();
            alert('Test users created successfully!');
        }
        
        function clearAllData() {
            if (confirm('Are you sure you want to clear all data?')) {
                localStorage.clear();
                updateUserList();
                alert('All data cleared!');
            }
        }
        
        function goToLogin() {
            window.location.href = 'index.html';
        }
    </script>
</body>
</html> 
{"name": "crm-backend", "version": "1.0.0", "description": "CRM System Backend API with PostgreSQL", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["crm", "api", "postgresql", "express"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "compression": "^1.8.1", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-session": "^1.18.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "multer": "^2.0.2", "pg": "^8.16.3", "rate-limit-redis": "^4.2.0", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.3", "uuid": "^9.0.1", "validator": "^13.11.0", "winston": "^3.11.0", "xss": "^1.0.15"}}
// Deals functionality
document.addEventListener('DOMContentLoaded', function() {
    // Automation: notify for overdue deals
    function notifyOverdueDeals() {
        const now = new Date();
        const overdueDeals = deals.filter(deal => {
            const closeDate = new Date(deal.closeDate);
            return closeDate < now && deal.stage !== 'Closed Won' && deal.stage !== 'Closed Lost';
        });
        if (overdueDeals.length) {
            overdueDeals.forEach(deal => {
                showMessage(`Deal "${deal.title}" is overdue!`, 'error');
            });
        }
    }

    // Logout functionality
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function() {
            auth.logout();
        });
    }
    
    // Initialize deals data
    let deals = JSON.parse(localStorage.getItem('deals') || '[]');
    
    // Add sample data if empty
    if (deals.length === 0) {
        deals = [
            { id: 1, title: 'ABC Corp Contract', customer: '<PERSON>', value: 50000, stage: 'Negotiation', closeDate: '2024-03-15', owner: loggedInUser, description: 'Software licensing contract', createdAt: new Date().toISOString() },
            { id: 2, title: 'XYZ Inc Partnership', customer: 'Jane Smith', value: 75000, stage: 'Proposal', closeDate: '2024-04-01', owner: loggedInUser, description: 'Strategic partnership agreement', createdAt: new Date().toISOString() },
            { id: 3, title: 'DEF Ltd Service', customer: 'Bob Johnson', value: 25000, stage: 'Qualification', closeDate: '2024-03-30', owner: loggedInUser, description: 'Consulting services', createdAt: new Date().toISOString() }
        ];
        localStorage.setItem('deals', JSON.stringify(deals));
    }
    
    // Form elements
    const addDealBtn = document.getElementById('addDealBtn');
    const dealFormContainer = document.getElementById('dealFormContainer');
    const dealForm = document.getElementById('dealForm');
    const formTitle = document.getElementById('formTitle');
    const cancelDealBtn = document.getElementById('cancelDealBtn');
    const dealSearch = document.getElementById('dealSearch');
    const searchBtn = document.getElementById('searchBtn');
    const dealsTableBody = document.getElementById('dealsTableBody');
    const dealCustomerSelect = document.getElementById('dealCustomer');
    const dealOwnerInput = document.getElementById('dealOwner');
    
    // Load customers for dropdown
    function loadCustomers() {
        const customers = JSON.parse(localStorage.getItem('customers') || '[]');
        dealCustomerSelect.innerHTML = '<option value="">Select Customer</option>';
        customers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.name;
            option.textContent = customer.name;
            dealCustomerSelect.appendChild(option);
        });
    }
    
    // Display deals
    function displayDeals(dealsToShow = deals) {
        dealsTableBody.innerHTML = '';
        dealsToShow.forEach(deal => {
            const row = document.createElement('tr');
            const closeDate = new Date(deal.closeDate);
            const isOverdue = closeDate < new Date() && deal.stage !== 'Closed Won' && deal.stage !== 'Closed Lost';
            // AI: Win prediction
            let winProb = 0.5;
            if (deal.stage === 'Closed Won') winProb = 1.0;
            else if (deal.stage === 'Closed Lost') winProb = 0.0;
            else if (deal.stage === 'Negotiation') winProb = 0.8;
            else if (deal.stage === 'Proposal') winProb = 0.7;
            else if (deal.stage === 'Qualification') winProb = 0.6;
            else if (deal.stage === 'Prospecting') winProb = 0.4;
            if (isOverdue) winProb -= 0.2;
            if (deal.value > 50000) winProb += 0.1;
            winProb = Math.max(0, Math.min(1, winProb));
            // AI: Smart suggestion
            let suggestion = '';
            if (isOverdue) suggestion = 'Follow up! Deal is overdue.';
            else if (deal.stage === 'Negotiation' && !isOverdue) suggestion = 'Send final offer.';
            else if (deal.stage === 'Proposal') suggestion = 'Check for client feedback.';
            row.innerHTML = `
                <td>${deal.title}</td>
                <td>${deal.customer}</td>
                <td>$${deal.value.toLocaleString()}</td>
                <td><span class="stage-${deal.stage.toLowerCase().replace(' ', '-')}">${deal.stage}</span></td>
                <td class="${isOverdue ? 'overdue' : ''}">${deal.closeDate}</td>
                <td>${deal.owner}</td>
                <td>
                    <button class="btn-primary" onclick="editDeal(${deal.id})">Edit</button>
                    <button class="btn-secondary" onclick="deleteDeal(${deal.id})">Delete</button>
                    <button class="btn-primary" onclick="showDealNotes(${deal.id})">Notes</button>
                </td>
                <td><span title="Win Probability">${(winProb*100).toFixed(0)}%</span></td>
                <td>${suggestion}</td>
            `;
            dealsTableBody.appendChild(row);
        });
    }
    
    // Show form
    function showForm(mode = 'add', deal = null) {
        dealFormContainer.style.display = 'block';
        loadCustomers();
        
        if (mode === 'add') {
            formTitle.textContent = 'Add New Deal';
            dealForm.reset();
            document.getElementById('dealId').value = '';
            dealOwnerInput.value = loggedInUser;
        } else {
            formTitle.textContent = 'Edit Deal';
            document.getElementById('dealId').value = deal.id;
            document.getElementById('dealTitle').value = deal.title;
            document.getElementById('dealValue').value = deal.value;
            document.getElementById('dealCustomer').value = deal.customer;
            document.getElementById('dealStage').value = deal.stage;
            document.getElementById('dealCloseDate').value = deal.closeDate;
            document.getElementById('dealOwner').value = deal.owner;
            document.getElementById('dealDescription').value = deal.description || '';
        }
    }
    
    // Hide form
    function hideForm() {
        dealFormContainer.style.display = 'none';
        dealForm.reset();
    }
    
    // Add deal button
    if (addDealBtn) {
        addDealBtn.addEventListener('click', () => showForm('add'));
    }
    
    // Cancel button
    if (cancelDealBtn) {
        cancelDealBtn.addEventListener('click', hideForm);
    }
    
    // Form submission
    if (dealForm) {
        dealForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const dealId = document.getElementById('dealId').value;
            const dealData = {
                title: document.getElementById('dealTitle').value.trim(),
                value: parseFloat(document.getElementById('dealValue').value),
                customer: document.getElementById('dealCustomer').value,
                stage: document.getElementById('dealStage').value,
                closeDate: document.getElementById('dealCloseDate').value,
                owner: document.getElementById('dealOwner').value,
                description: document.getElementById('dealDescription').value.trim(),
                updatedAt: new Date().toISOString()
            };
            
            if (!dealData.title || !dealData.customer || !dealData.value) {
                alert('Title, customer, and value are required!');
                return;
            }
            
            if (dealId) {
                // Edit existing deal
                const index = deals.findIndex(d => d.id == dealId);
                if (index !== -1) {
                    deals[index] = { ...deals[index], ...dealData };
                }
            } else {
                // Add new deal
                dealData.id = Date.now();
                dealData.createdAt = new Date().toISOString();
                deals.push(dealData);
            }
            
            localStorage.setItem('deals', JSON.stringify(deals));
            displayDeals();
            hideForm();
            showMessage('Deal saved successfully!', 'success');
        });
    }
    
    // Search functionality
    if (dealSearch && searchBtn) {
        searchBtn.addEventListener('click', function() {
            const searchTerm = dealSearch.value.toLowerCase();
            const filteredDeals = deals.filter(deal => 
                deal.title.toLowerCase().includes(searchTerm) ||
                deal.customer.toLowerCase().includes(searchTerm) ||
                deal.stage.toLowerCase().includes(searchTerm)
            );
            displayDeals(filteredDeals);
        });
        
        dealSearch.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchBtn.click();
            }
        });
    }
    
    // Global functions for edit and delete
    window.editDeal = function(id) {
        const deal = deals.find(d => d.id == id);
        if (deal) {
            showForm('edit', deal);
        }
    };
    
    window.deleteDeal = function(id) {
        if (confirm('Are you sure you want to delete this deal?')) {
            deals = deals.filter(d => d.id != id);
            localStorage.setItem('deals', JSON.stringify(deals));
            displayDeals();
            showMessage('Deal deleted successfully!', 'success');
        }
    };
    
    // Initial display
    displayDeals();
    notifyOverdueDeals();

    // Deal Notes modal logic
    const dealNotesModal = document.getElementById('dealNotesModal');
    const closeDealNotesModal = document.getElementById('closeDealNotesModal');
    const dealNotesList = document.getElementById('dealNotesList');
    const newDealNoteText = document.getElementById('newDealNoteText');
    const saveDealNoteBtn = document.getElementById('saveDealNoteBtn');
    let currentDealId = null;

    window.showDealNotes = function(id) {
        currentDealId = id;
        const notesData = JSON.parse(localStorage.getItem('dealNotes') || '{}');
        const notes = notesData[id] || [];
        dealNotesList.innerHTML = notes.length ? notes.map(n => `<div class="note-item">${n.text} <span style='font-size:10px;color:#888;'>${n.date}</span></div>`).join('') : '<em>No notes yet.</em>';
        newDealNoteText.value = '';
        dealNotesModal.style.display = 'block';
    };

    if (closeDealNotesModal) {
        closeDealNotesModal.onclick = function() {
            dealNotesModal.style.display = 'none';
        };
    }

    if (saveDealNoteBtn) {
        saveDealNoteBtn.onclick = function() {
            const text = newDealNoteText.value.trim();
            if (!text) return;
            const notesData = JSON.parse(localStorage.getItem('dealNotes') || '{}');
            if (!notesData[currentDealId]) notesData[currentDealId] = [];
            notesData[currentDealId].push({ text, date: new Date().toLocaleString() });
            localStorage.setItem('dealNotes', JSON.stringify(notesData));
            showDealNotes(currentDealId);
        };
    }

    // Kanban board logic
    const kanbanViewBtn = document.getElementById('kanbanViewBtn');
    const dealsTableContainer = document.getElementById('dealsTableContainer');
    const kanbanBoard = document.getElementById('kanbanBoard');

    function renderKanban() {
        const stages = ["Prospecting","Qualification","Proposal","Negotiation","Closed Won","Closed Lost"];
        stages.forEach(stage => {
            const list = document.getElementById('kanban-' + stage.replace(' ', '-'));
            list.innerHTML = '';
            deals.filter(d => d.stage === stage).forEach(deal => {
                const card = document.createElement('div');
                card.className = 'kanban-card';
                card.draggable = true;
                card.dataset.id = deal.id;
                card.innerHTML = `<strong>${deal.title}</strong><br>$${deal.value.toLocaleString()}<br>${deal.customer}`;
                card.ondragstart = function(e) {
                    e.dataTransfer.setData('dealId', deal.id);
                };
                list.appendChild(card);
            });
            // Drag-and-drop logic
            list.ondragover = function(e) { e.preventDefault(); };
            list.ondrop = function(e) {
                e.preventDefault();
                const dealId = e.dataTransfer.getData('dealId');
                const index = deals.findIndex(d => d.id == dealId);
                if (index !== -1) {
                    deals[index].stage = stage;
                    localStorage.setItem('deals', JSON.stringify(deals));
                    renderKanban();
                }
            };
        });
    }

    if (kanbanViewBtn) {
        kanbanViewBtn.onclick = function() {
            // Always hide table and show Kanban if not visible
            if (kanbanBoard.style.display === 'none' || kanbanBoard.style.display === '' || getComputedStyle(kanbanBoard).display === 'none') {
                dealsTableContainer.style.display = 'none';
                kanbanBoard.style.display = 'block';
                renderKanban();
                kanbanViewBtn.textContent = 'Table View';
            } else {
                dealsTableContainer.style.display = 'block';
                kanbanBoard.style.display = 'none';
                kanbanViewBtn.textContent = 'Kanban Board';
            }
        };
    }
});

function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: bold;
        z-index: 1000;
        ${type === 'error' ? 'background-color: #e74c3c;' : 'background-color: #27ae60;'}
    `;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 3000);
}
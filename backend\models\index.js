const { sequelize } = require('../config/database');
const User = require('./User');
const Customer = require('./Customer');
const Contact = require('./Contact');
const Deal = require('./Deal');
const Task = require('./Task');
const Activity = require('./Activity');
const Calendar = require('./Calendar');

// Define associations

// User associations
User.hasMany(Customer, { as: 'assignedCustomers', foreignKey: 'assignedToId' });
User.hasMany(Customer, { as: 'createdCustomers', foreignKey: 'createdById' });
User.hasMany(Deal, { as: 'assignedDeals', foreignKey: 'assignedToId' });
User.hasMany(Deal, { as: 'createdDeals', foreignKey: 'createdById' });
User.hasMany(Task, { as: 'assignedTasks', foreignKey: 'assignedToId' });
User.hasMany(Task, { as: 'createdTasks', foreignKey: 'createdById' });
User.hasMany(Activity, { as: 'activities', foreignKey: 'performedById' });
User.hasMany(Contact, { as: 'createdContacts', foreignKey: 'createdById' });

// Customer associations
Customer.belongsTo(User, { as: 'assignedTo', foreignKey: 'assignedToId' });
Customer.belongsTo(User, { as: 'createdBy', foreignKey: 'createdById' });
Customer.hasMany(Contact, { as: 'contacts', foreignKey: 'customerId' });
Customer.hasMany(Deal, { as: 'deals', foreignKey: 'customerId' });
Customer.hasMany(Task, { as: 'tasks', foreignKey: 'customerId' });
Customer.hasMany(Activity, { as: 'activities', foreignKey: 'customerId' });

// Contact associations
Contact.belongsTo(Customer, { as: 'customer', foreignKey: 'customerId' });
Contact.belongsTo(User, { as: 'createdBy', foreignKey: 'createdById' });
Contact.hasMany(Deal, { as: 'deals', foreignKey: 'contactId' });
Contact.hasMany(Task, { as: 'tasks', foreignKey: 'contactId' });
Contact.hasMany(Activity, { as: 'activities', foreignKey: 'contactId' });

// Deal associations
Deal.belongsTo(Customer, { as: 'customer', foreignKey: 'customerId' });
Deal.belongsTo(Contact, { as: 'contact', foreignKey: 'contactId' });
Deal.belongsTo(User, { as: 'assignedTo', foreignKey: 'assignedToId' });
Deal.belongsTo(User, { as: 'createdBy', foreignKey: 'createdById' });
Deal.hasMany(Task, { as: 'tasks', foreignKey: 'dealId' });
Deal.hasMany(Activity, { as: 'activities', foreignKey: 'dealId' });

// Task associations
Task.belongsTo(Customer, { as: 'customer', foreignKey: 'customerId' });
Task.belongsTo(Contact, { as: 'contact', foreignKey: 'contactId' });
Task.belongsTo(Deal, { as: 'deal', foreignKey: 'dealId' });
Task.belongsTo(User, { as: 'assignedTo', foreignKey: 'assignedToId' });
Task.belongsTo(User, { as: 'createdBy', foreignKey: 'createdById' });

// Activity associations
Activity.belongsTo(User, { as: 'performedBy', foreignKey: 'performedById' });
Activity.belongsTo(Customer, { as: 'customer', foreignKey: 'customerId' });
Activity.belongsTo(Contact, { as: 'contact', foreignKey: 'contactId' });
Activity.belongsTo(Deal, { as: 'deal', foreignKey: 'dealId' });

// Calendar associations
Calendar.belongsTo(User, { as: 'createdBy', foreignKey: 'createdById' });
Calendar.belongsTo(User, { as: 'assignedTo', foreignKey: 'assignedToId' });
Calendar.belongsTo(Customer, { as: 'customer', foreignKey: 'customerId' });
Calendar.belongsTo(Contact, { as: 'contact', foreignKey: 'contactId' });
Calendar.belongsTo(Deal, { as: 'deal', foreignKey: 'dealId' });
Calendar.belongsTo(Task, { as: 'task', foreignKey: 'taskId' });

// Add Calendar associations to other models
User.hasMany(Calendar, { as: 'createdEvents', foreignKey: 'createdById' });
User.hasMany(Calendar, { as: 'assignedEvents', foreignKey: 'assignedToId' });
Customer.hasMany(Calendar, { as: 'events', foreignKey: 'customerId' });
Contact.hasMany(Calendar, { as: 'events', foreignKey: 'contactId' });
Deal.hasMany(Calendar, { as: 'events', foreignKey: 'dealId' });
Task.hasOne(Calendar, { as: 'event', foreignKey: 'taskId' });

module.exports = {
  sequelize,
  User,
  Customer,
  Contact,
  Deal,
  Task,
  Activity,
  Calendar
};

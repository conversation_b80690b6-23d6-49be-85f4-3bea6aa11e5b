// Helper functions
function isAuthenticated() {
    return !!localStorage.getItem('authToken') || !!localStorage.getItem('loggedInUser');
}

function getFormData(form) {
    const formData = new FormData(form);
    const data = {};
    for (const [key, value] of formData.entries()) {
        data[key] = value;
    }
    return data;
}

function validateRequired(value) {
    return value !== null && value !== undefined && value.toString().trim() !== '';
}

function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(String(email).toLowerCase());
}

// Login functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check if already authenticated
    if (isAuthenticated()) {
        window.location.href = 'dashboard.html';
        return;
    }

    const loginForm = document.getElementById('loginForm');
    const messageArea = document.getElementById('messageArea');
    
    // Subscribe to loading state changes
    api.onLoadingChange('login', (isLoading) => {
        const submitButton = loginForm?.querySelector('button[type="submit"]');
        if (submitButton) {
            submitButton.disabled = isLoading;
            submitButton.innerHTML = isLoading 
                ? '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Logging in...'
                : '<i class="bi bi-box-arrow-in-right me-2"></i>Login';
        }
    });
    
    if (loginForm) {
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // Clear previous messages
            if (messageArea) messageArea.innerHTML = '';
            
            // Get form data
            const formData = getFormData(loginForm);
            const { email, password } = formData;
            
            // Client-side validation
            const validationErrors = [];
            
            if (!validateRequired(email)) {
                validationErrors.push('Email is required');
            } else if (!validateEmail(email)) {
                validationErrors.push('Please enter a valid email address');
            }
            
            if (!validateRequired(password)) {
                validationErrors.push('Password is required');
            } else if (password.length < 6) {
                validationErrors.push('Password must be at least 6 characters');
            }
            
            if (validationErrors.length > 0) {
                if (messageArea) {
                    showError('messageArea', { message: validationErrors.join('<br>') });
                } else {
                    showMessage(validationErrors.join(', '), 'error');
                }
                return;
            }
            
            try {
                // Try API authentication
                const response = await api.login(email, password);
                
                if (response.success) {
                    if (messageArea) {
                        showSuccess('messageArea', 'Login successful! Redirecting...');
                    } else {
                        showMessage('Login successful! Redirecting...', 'success');
                    }
                    
                    // Redirect to dashboard or intended page
                    const redirect = new URLSearchParams(window.location.search).get('redirect') || 'dashboard.html';
                    setTimeout(() => {
                        window.location.href = redirect;
                    }, 1000);
                } else {
                    throw new Error(response.message || 'Login failed');
                }
            } catch (error) {
                console.error('Login error:', error);
                
                // If API fails, try fallback local auth
                if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
                    // Check if local auth system is available
                    if (typeof auth !== 'undefined' && auth.login(email, password)) {
                        showMessage('Login successful! Redirecting...', 'success');
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 1000);
                        return;
                    }
                }
                
                // Show error message
                const errorMessage = error.status === 401 
                    ? 'Invalid email or password' 
                    : error.message || 'Login failed. Please try again.';
                    
                if (messageArea) {
                    showError('messageArea', { message: errorMessage });
                } else {
                    showMessage(errorMessage, 'error');
                }
            }
        });
    }
    
    // Show/hide password toggle
    const passwordToggle = document.querySelector('.password-toggle');
    const passwordInput = document.getElementById('password');
    
    if (passwordToggle && passwordInput) {
        passwordToggle.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            // Toggle icon
            const icon = this.querySelector('i');
            if (icon) {
                icon.classList.toggle('bi-eye');
                icon.classList.toggle('bi-eye-slash');
            }
        });
    }
    
    // Remember me functionality
    const rememberCheck = document.getElementById('rememberMe');
    const emailInput = document.getElementById('email');
    
    // Load saved email if remember me was checked
    const savedEmail = localStorage.getItem('rememberedEmail');
    if (savedEmail && emailInput) {
        emailInput.value = savedEmail;
        if (rememberCheck) rememberCheck.checked = true;
    }
    
    // Save/remove email based on remember me
    if (loginForm && rememberCheck) {
        loginForm.addEventListener('submit', function() {
            if (rememberCheck.checked && emailInput) {
                localStorage.setItem('rememberedEmail', emailInput.value);
            } else {
                localStorage.removeItem('rememberedEmail');
            }
        });
    }
    
    // Demo accounts for fallback local authentication
    if (typeof auth !== 'undefined') {
        const demoAccounts = [
            {
                id: 1,
                fullName: 'Demo Administrator',
                email: '<EMAIL>',
                username: 'admin',
                password: btoa('admin123'),
                department: 'IT',
                role: 'admin',
                createdAt: new Date().toISOString()
            },
            {
                id: 2,
                fullName: 'Demo Manager',
                email: '<EMAIL>',
                username: 'manager',
                password: btoa('manager123'),
                department: 'Sales',
                role: 'manager',
                createdAt: new Date().toISOString()
            },
            {
                id: 3,
                fullName: 'Demo User',
                email: '<EMAIL>',
                username: 'user',
                password: btoa('user123'),
                department: 'Support',
                role: 'user',
                createdAt: new Date().toISOString()
            },
            {
                id: 4,
                fullName: 'Demo Viewer',
                email: '<EMAIL>',
                username: 'viewer',
                password: btoa('viewer123'),
                department: 'Support',
                role: 'viewer',
                createdAt: new Date().toISOString()
            }
        ];
        
        let users = JSON.parse(localStorage.getItem('users') || '[]');
        // Remove any users with demo usernames and replace with correct demo accounts
        users = users.filter(u => !['admin','manager','user','viewer'].includes(u.username));
        users = demoAccounts.concat(users);
        localStorage.setItem('users', JSON.stringify(users));
        console.log('Demo accounts ensured for fallback authentication');
    }
});

// Fallback message display for pages without message area
function showMessage(message, type) {
    // Try to use the global showToast function if available
    if (typeof showToast === 'function') {
        const toastType = type === 'error' ? 'danger' : 'success';
        showToast(message, toastType);
        return;
    }
    
    // Fallback to custom message display
    const existingMessage = document.querySelector('.message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `alert alert-${type === 'error' ? 'danger' : 'success'} message`;
    messageDiv.innerHTML = `
        <i class="bi bi-${type === 'error' ? 'exclamation-circle' : 'check-circle'}-fill me-2"></i>
        ${message}
    `;
    
    const form = document.getElementById('loginForm');
    if (form && form.parentNode) {
        form.parentNode.insertBefore(messageDiv, form);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, 5000);
    }
}

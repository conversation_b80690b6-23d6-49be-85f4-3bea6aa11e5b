// Calendar functionality
document.addEventListener('DOMContentLoaded', function() {
    // Logout functionality
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function() {
            auth.logout();
        });
    }
    
    // Calendar variables
    const currentDate = new Date();
    let currentMonth = currentDate.getMonth();
    let currentYear = currentDate.getFullYear();
    
    // Load events from localStorage
    let events = JSON.parse(localStorage.getItem('events') || '[]');
    
    // Add sample events if empty
    if (events.length === 0) {
        events = [
            { id: 1, title: 'Client Meeting', date: new Date(Date.now() + 86400000).toISOString().split('T')[0], startTime: '10:00', endTime: '11:00', description: 'Meeting with ABC Corp', relatedTo: 'customer', relatedId: '<PERSON>', createdAt: new Date().toISOString() },
            { id: 2, title: 'Team Standup', date: new Date(Date.now() + 172800000).toISOString().split('T')[0], startTime: '09:00', endTime: '09:30', description: 'Daily team meeting', relatedTo: '', relatedId: '', createdAt: new Date().toISOString() }
        ];
        localStorage.setItem('events', JSON.stringify(events));
    }
    
    // Form elements
    const addEventBtn = document.getElementById('addEventBtn');
    const eventFormContainer = document.getElementById('eventFormContainer');
    const eventForm = document.getElementById('eventForm');
    const eventFormTitle = document.getElementById('eventFormTitle');
    const cancelEventBtn = document.getElementById('cancelEventBtn');
    const eventRelatedTo = document.getElementById('eventRelatedTo');
    const eventCustomerGroup = document.getElementById('eventCustomerGroup');
    const eventDealGroup = document.getElementById('eventDealGroup');
    const eventCustomer = document.getElementById('eventCustomer');
    const eventDeal = document.getElementById('eventDeal');
    const upcomingEventsList = document.getElementById('upcomingEventsList');
    
    // Load related data for dropdowns
    function loadRelatedData() {
        const customers = JSON.parse(localStorage.getItem('customers') || '[]');
        const deals = JSON.parse(localStorage.getItem('deals') || '[]');
        
        // Load customers
        eventCustomer.innerHTML = '<option value="">Select Customer</option>';
        customers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.name;
            option.textContent = customer.name;
            eventCustomer.appendChild(option);
        });
        
        // Load deals
        eventDeal.innerHTML = '<option value="">Select Deal</option>';
        deals.forEach(deal => {
            const option = document.createElement('option');
            option.value = deal.title;
            option.textContent = deal.title;
            eventDeal.appendChild(option);
        });
    }
    
    // Handle related to selection
    if (eventRelatedTo) {
        eventRelatedTo.addEventListener('change', function() {
            eventCustomerGroup.style.display = 'none';
            eventDealGroup.style.display = 'none';
            
            if (this.value === 'customer') {
                eventCustomerGroup.style.display = 'block';
            } else if (this.value === 'deal') {
                eventDealGroup.style.display = 'block';
            }
        });
    }
    
    // Render calendar
    function renderCalendar() {
        const monthYearElement = document.getElementById('currentMonthYear');
        const daysElement = document.getElementById('calendarDays');
        
        if (monthYearElement && daysElement) {
            // Set month and year header
            const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
            monthYearElement.textContent = `${monthNames[currentMonth]} ${currentYear}`;
            
            // Clear days
            daysElement.innerHTML = '';
            
            // Get first day of month
            const firstDay = new Date(currentYear, currentMonth, 1).getDay();
            
            // Get number of days in month
            const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
            
            // Get number of days in previous month
            const daysInPrevMonth = new Date(currentYear, currentMonth, 0).getDate();
            
            // Add days from previous month
            for (let i = firstDay - 1; i >= 0; i--) {
                const dayElement = document.createElement('div');
                dayElement.className = 'day other-month';
                dayElement.innerHTML = `
                    <div class="day-number">${daysInPrevMonth - i}</div>
                `;
                daysElement.appendChild(dayElement);
            }
            
            // Add days from current month
            const today = new Date();
            for (let i = 1; i <= daysInMonth; i++) {
                const dayElement = document.createElement('div');
                dayElement.className = 'day';
                
                // Check if today
                if (i === today.getDate() && currentMonth === today.getMonth() && currentYear === today.getFullYear()) {
                    dayElement.classList.add('today');
                }
                
                // Add events for this day
                const currentDate = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
                const dayEvents = events.filter(event => event.date === currentDate);
                let eventsHtml = '';
                
                dayEvents.forEach(event => {
                    eventsHtml += `<div class="day-events" title="${event.title} - ${event.startTime}">${event.title}</div>`;
                });
                
                dayElement.innerHTML = `
                    <div class="day-number">${i}</div>
                    ${eventsHtml}
                `;
                daysElement.appendChild(dayElement);
            }
            
            // Add days from next month to fill grid
            const totalCells = 42; // 6 rows x 7 columns
            const remainingCells = totalCells - (firstDay + daysInMonth);
            for (let i = 1; i <= remainingCells; i++) {
                const dayElement = document.createElement('div');
                dayElement.className = 'day other-month';
                dayElement.innerHTML = `
                    <div class="day-number">${i}</div>
                `;
                daysElement.appendChild(dayElement);
            }
        }
        
        // Render events sidebar
        renderEventsSidebar();
    }
    
    // Render events sidebar
    function renderEventsSidebar() {
        if (upcomingEventsList) {
            upcomingEventsList.innerHTML = '';
            
            // Get upcoming events (next 7 days)
            const today = new Date();
            const nextWeek = new Date(today.getTime() + (7 * 24 * 60 * 60 * 1000));
            
            const upcomingEvents = events.filter(event => {
                const eventDate = new Date(event.date);
                return eventDate >= today && eventDate <= nextWeek;
            }).sort((a, b) => new Date(a.date) - new Date(b.date));
            
            upcomingEvents.forEach(event => {
                const li = document.createElement('li');
                li.innerHTML = `
                    <div class="event-item">
                        <div class="event-title">${event.title}</div>
                        <div class="event-date">${new Date(event.date).toLocaleDateString()} ${event.startTime}</div>
                        ${event.description ? `<div class="event-description">${event.description}</div>` : ''}
                    </div>
                `;
                upcomingEventsList.appendChild(li);
            });
            
            // If no upcoming events, show message
            if (upcomingEvents.length === 0) {
                const li = document.createElement('li');
                li.textContent = 'No upcoming events';
                upcomingEventsList.appendChild(li);
            }
        }
    }
    
    // Show event form
    function showEventForm(mode = 'add', event = null) {
        eventFormContainer.style.display = 'block';
        loadRelatedData();
        
        if (mode === 'add') {
            eventFormTitle.textContent = 'Add New Event';
            eventForm.reset();
            document.getElementById('eventId').value = '';
            eventCustomerGroup.style.display = 'none';
            eventDealGroup.style.display = 'none';
            document.getElementById('eventDate').value = new Date().toISOString().split('T')[0];
        } else {
            eventFormTitle.textContent = 'Edit Event';
            document.getElementById('eventId').value = event.id;
            document.getElementById('eventTitle').value = event.title;
            document.getElementById('eventDate').value = event.date;
            document.getElementById('eventStartTime').value = event.startTime;
            document.getElementById('eventEndTime').value = event.endTime;
            document.getElementById('eventDescription').value = event.description || '';
            document.getElementById('eventRelatedTo').value = event.relatedTo || '';
            
            if (event.relatedTo === 'customer') {
                eventCustomerGroup.style.display = 'block';
                eventCustomer.value = event.relatedId;
            } else if (event.relatedTo === 'deal') {
                eventDealGroup.style.display = 'block';
                eventDeal.value = event.relatedId;
            }
        }
    }
    
    // Hide event form
    function hideEventForm() {
        eventFormContainer.style.display = 'none';
        eventForm.reset();
        eventCustomerGroup.style.display = 'none';
        eventDealGroup.style.display = 'none';
    }
    
    // Add event button
    if (addEventBtn) {
        addEventBtn.addEventListener('click', () => showEventForm('add'));
    }
    
    // Cancel button
    if (cancelEventBtn) {
        cancelEventBtn.addEventListener('click', hideEventForm);
    }
    
    // Event form submission
    if (eventForm) {
        eventForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const eventId = document.getElementById('eventId').value;
            const eventData = {
                title: document.getElementById('eventTitle').value.trim(),
                date: document.getElementById('eventDate').value,
                startTime: document.getElementById('eventStartTime').value,
                endTime: document.getElementById('eventEndTime').value,
                description: document.getElementById('eventDescription').value.trim(),
                relatedTo: document.getElementById('eventRelatedTo').value,
                relatedId: document.getElementById('eventRelatedTo').value === 'customer' ? 
                    document.getElementById('eventCustomer').value : 
                    document.getElementById('eventRelatedTo').value === 'deal' ? 
                    document.getElementById('eventDeal').value : '',
                updatedAt: new Date().toISOString()
            };
            
            if (!eventData.title || !eventData.date || !eventData.startTime || !eventData.endTime) {
                alert('Title, date, start time, and end time are required!');
                return;
            }
            
            if (eventId) {
                // Edit existing event
                const index = events.findIndex(e => e.id == eventId);
                if (index !== -1) {
                    events[index] = { ...events[index], ...eventData };
                }
            } else {
                // Add new event
                eventData.id = Date.now();
                eventData.createdAt = new Date().toISOString();
                events.push(eventData);
            }
            
            localStorage.setItem('events', JSON.stringify(events));
            renderCalendar();
            hideEventForm();
            showMessage('Event saved successfully!', 'success');
        });
    }
    
    // Navigation buttons
    const prevMonthBtn = document.getElementById('prevMonthBtn');
    const nextMonthBtn = document.getElementById('nextMonthBtn');
    
    if (prevMonthBtn) {
        prevMonthBtn.addEventListener('click', function() {
            currentMonth--;
            if (currentMonth < 0) {
                currentMonth = 11;
                currentYear--;
            }
            renderCalendar();
        });
    }
    
    if (nextMonthBtn) {
        nextMonthBtn.addEventListener('click', function() {
            currentMonth++;
            if (currentMonth > 11) {
                currentMonth = 0;
                currentYear++;
            }
            renderCalendar();
        });
    }
    
    // Initial render
    renderCalendar();
});

function showMessage(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: bold;
        z-index: 1000;
        ${type === 'error' ? 'background-color: #e74c3c;' : 'background-color: #27ae60;'}
    `;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.remove();
        }
    }, 3000);
}
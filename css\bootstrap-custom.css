/* Custom Bootstrap 5 Theme for CRM System */

:root {
  /* Professional Color Scheme */
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;
  --info-color: #06b6d4;
  --dark-color: #1e293b;
  --light-color: #f8fafc;
  
  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f1f5f9;
  --bg-dark: #0f172a;
  
  /* Text Colors */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-light: #94a3b8;
  
  /* Border Colors */
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  
  /* Shadow */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Override Bootstrap Primary Color */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.text-primary {
  color: var(--primary-color) !important;
}

.bg-primary {
  background-color: var(--primary-color) !important;
}

/* Body and Typography */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 1rem;
}

/* Custom Navbar Styling */
.navbar-custom {
  background-color: var(--bg-primary);
  box-shadow: var(--shadow);
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
  color: var(--primary-color) !important;
}

.navbar-nav .nav-link {
  color: var(--text-primary);
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
  border-radius: 0.375rem;
}

.navbar-nav .nav-link:hover {
  color: var(--primary-color);
  background-color: var(--bg-secondary);
}

.navbar-nav .nav-link.active {
  color: var(--primary-color);
  background-color: rgba(37, 99, 235, 0.1);
}

/* User Profile Dropdown */
.user-dropdown {
  position: relative;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
}

/* Card Styling */
.card {
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
  background-color: var(--bg-primary);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  font-weight: 600;
  color: var(--text-primary);
}

/* Stat Cards for Dashboard */
.stat-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
  height: 100%;
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.stat-card h3 {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.5rem;
}

.stat-card .stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0;
}

.stat-card .stat-change {
  font-size: 0.875rem;
  color: var(--success-color);
  margin-top: 0.5rem;
}

.stat-card .stat-change.negative {
  color: var(--danger-color);
}

/* Table Styling */
.table-responsive {
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: var(--bg-primary);
}

.table {
  margin-bottom: 0;
}

.table thead th {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  border-bottom: 2px solid var(--border-color);
  padding: 1rem;
}

.table tbody tr {
  transition: background-color 0.2s ease;
}

.table tbody tr:hover {
  background-color: var(--bg-secondary);
}

.table tbody td {
  padding: 1rem;
  vertical-align: middle;
  color: var(--text-primary);
}

/* Sortable Table Headers */
.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
  padding-right: 1.5rem;
}

.sortable::after {
  content: '↕';
  position: absolute;
  right: 0.5rem;
  color: var(--text-light);
}

.sortable.asc::after {
  content: '↑';
  color: var(--primary-color);
}

.sortable.desc::after {
  content: '↓';
  color: var(--primary-color);
}

/* Form Styling */
.form-control, .form-select {
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  padding: 0.625rem 0.875rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-label {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

/* Form Validation Feedback */
.was-validated .form-control:valid, .form-control.is-valid {
  border-color: var(--success-color);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2310b981' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:invalid, .form-control.is-invalid {
  border-color: var(--danger-color);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23ef4444' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23ef4444' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.5'/%3e%3ccircle cx='3' r='.5'/%3e%3ccircle cy='3' r='.5'/%3e%3ccircle cx='3' cy='3' r='.5'/%3e%3c/svg%3E");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.invalid-feedback {
  font-size: 0.75rem;
  color: var(--danger-color);
  margin-top: 0.25rem;
}

.valid-feedback {
  font-size: 0.75rem;
  color: var(--success-color);
  margin-top: 0.25rem;
}

/* Badge Styling */
.badge {
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
}

/* Status Badges */
.badge-status-active {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.badge-status-pending {
  background-color: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.badge-status-inactive {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

/* Buttons */
.btn {
  border-radius: 0.375rem;
  font-weight: 500;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

/* Modal Styling */
.modal-content {
  border: none;
  border-radius: 0.5rem;
  box-shadow: var(--shadow-lg);
}

.modal-header {
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  border-radius: 0.5rem 0.5rem 0 0;
}

.modal-title {
  font-weight: 600;
  color: var(--text-primary);
}

/* Dropdown Styling */
.dropdown-menu {
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  box-shadow: var(--shadow-md);
  padding: 0.5rem;
}

.dropdown-item {
  border-radius: 0.25rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background-color: var(--bg-secondary);
  color: var(--primary-color);
}

/* Pagination */
.pagination {
  margin: 0;
}

.page-link {
  color: var(--text-primary);
  border-color: var(--border-color);
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

.page-link:hover {
  background-color: var(--bg-secondary);
  color: var(--primary-color);
  border-color: var(--border-color);
}

.page-item.active .page-link {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Utility Classes */
.text-muted {
  color: var(--text-secondary) !important;
}

.border-light {
  border-color: var(--border-light) !important;
}

/* Loading States */
.loading {
  position: relative;
  pointer-events: none;
  opacity: 0.6;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2rem;
  height: 2rem;
  border: 3px solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .navbar-nav {
    padding-top: 1rem;
  }
  
  .stat-card {
    margin-bottom: 1rem;
  }
  
  .stat-card .stat-value {
    font-size: 2rem;
  }
  
  .table {
    font-size: 0.875rem;
  }
  
  .btn {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }
}

/* Print Styles */
@media print {
  .navbar, .btn, .dropdown {
    display: none !important;
  }
  
  .card {
    border: 1px solid #ddd !important;
    box-shadow: none !important;
  }
}

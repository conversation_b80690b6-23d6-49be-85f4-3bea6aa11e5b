/**
 * CSRF Protection Middleware
 * 
 * Implements Cross-Site Request Forgery protection using double submit cookie pattern
 * and synchronizer token pattern
 */

const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');

/**
 * Token storage (In production, use Redis or similar)
 */
const tokenStore = new Map();

/**
 * Generate a secure CSRF token
 * @returns {string} CSRF token
 */
const generateToken = () => {
  return crypto.randomBytes(32).toString('hex');
};

/**
 * Verify CSRF token
 * @param {string} token - Token to verify
 * @param {string} sessionId - Session ID
 * @returns {boolean} Is valid
 */
const verifyToken = (token, sessionId) => {
  const storedToken = tokenStore.get(sessionId);
  return storedToken && crypto.timingSafeEqual(
    Buffer.from(storedToken),
    Buffer.from(token)
  );
};

/**
 * CSRF Protection middleware factory
 * @param {Object} options - Configuration options
 * @returns {Function} Express middleware
 */
const csrfProtection = (options = {}) => {
  const config = {
    cookie: {
      name: '_csrf',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    },
    header: 'x-csrf-token',
    skipRoutes: ['/api/auth/login', '/api/auth/register', '/api/health'],
    errorMessage: 'Invalid CSRF token',
    ...options
  };

  return (req, res, next) => {
    // Skip CSRF check for whitelisted routes
    if (config.skipRoutes.includes(req.path)) {
      return next();
    }

    // Skip CSRF for safe methods (GET, HEAD, OPTIONS)
    if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
      // Generate and attach token for subsequent requests
      const sessionId = req.session?.id || req.cookies?.sessionId || uuidv4();
      let token = tokenStore.get(sessionId);
      
      if (!token) {
        token = generateToken();
        tokenStore.set(sessionId, token);
        
        // Clean up old tokens periodically
        if (tokenStore.size > 10000) {
          const oldestKey = tokenStore.keys().next().value;
          tokenStore.delete(oldestKey);
        }
      }
      
      // Attach token to response
      res.locals.csrfToken = token;
      
      // Set cookie
      res.cookie(config.cookie.name, token, config.cookie);
      
      return next();
    }

    // For state-changing methods, verify CSRF token
    const sessionId = req.session?.id || req.cookies?.sessionId;
    if (!sessionId) {
      return res.status(403).json({
        success: false,
        message: 'No session found'
      });
    }

    // Get token from header or body
    const token = req.headers[config.header] || 
                  req.body?._csrf || 
                  req.query?._csrf;

    if (!token) {
      return res.status(403).json({
        success: false,
        message: 'CSRF token missing'
      });
    }

    // Verify token
    if (!verifyToken(token, sessionId)) {
      return res.status(403).json({
        success: false,
        message: config.errorMessage
      });
    }

    // Token is valid, continue
    next();
  };
};

/**
 * Double Submit Cookie CSRF Protection
 * More suitable for stateless applications
 */
const doubleSubmitCookie = (options = {}) => {
  const config = {
    cookieName: 'csrf-token',
    headerName: 'x-csrf-token',
    secret: process.env.CSRF_SECRET || 'default-secret-change-in-production',
    cookieOptions: {
      httpOnly: false, // Must be accessible by JavaScript
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000
    },
    skipRoutes: ['/api/auth/login', '/api/auth/register', '/api/health'],
    ...options
  };

  return (req, res, next) => {
    // Skip for whitelisted routes
    if (config.skipRoutes.includes(req.path)) {
      return next();
    }

    // For GET requests, generate and set token
    if (req.method === 'GET') {
      const token = crypto
        .createHmac('sha256', config.secret)
        .update(req.sessionID || req.ip + req.headers['user-agent'])
        .digest('hex');
      
      res.cookie(config.cookieName, token, config.cookieOptions);
      res.locals.csrfToken = token;
      
      return next();
    }

    // For other methods, verify token
    const cookieToken = req.cookies[config.cookieName];
    const headerToken = req.headers[config.headerName] || req.body?._csrf;

    if (!cookieToken || !headerToken) {
      return res.status(403).json({
        success: false,
        message: 'CSRF token missing'
      });
    }

    // Verify tokens match
    if (cookieToken !== headerToken) {
      return res.status(403).json({
        success: false,
        message: 'CSRF token mismatch'
      });
    }

    // Verify token is valid for this session
    const expectedToken = crypto
      .createHmac('sha256', config.secret)
      .update(req.sessionID || req.ip + req.headers['user-agent'])
      .digest('hex');

    if (cookieToken !== expectedToken) {
      return res.status(403).json({
        success: false,
        message: 'Invalid CSRF token'
      });
    }

    next();
  };
};

/**
 * CSRF token endpoint
 * Provides CSRF token for AJAX requests
 */
const csrfTokenEndpoint = (req, res) => {
  const token = res.locals.csrfToken || req.cookies['csrf-token'];
  
  if (!token) {
    return res.status(500).json({
      success: false,
      message: 'CSRF token not available'
    });
  }

  res.json({
    success: true,
    csrfToken: token
  });
};

/**
 * Clean up expired tokens
 * Should be run periodically
 */
const cleanupTokens = () => {
  // In a production environment, this would clean up expired tokens from Redis
  if (tokenStore.size > 5000) {
    const entriesToDelete = tokenStore.size - 5000;
    const iterator = tokenStore.keys();
    
    for (let i = 0; i < entriesToDelete; i++) {
      const key = iterator.next().value;
      tokenStore.delete(key);
    }
  }
};

// Run cleanup every hour
setInterval(cleanupTokens, 60 * 60 * 1000);

module.exports = {
  csrfProtection,
  doubleSubmitCookie,
  csrfTokenEndpoint,
  generateToken,
  verifyToken
};

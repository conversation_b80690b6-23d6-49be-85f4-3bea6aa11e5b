const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Deal = sequelize.define('Deal', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  value: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'USD'
  },
  stage: {
    type: DataTypes.ENUM('qualification', 'needs_analysis', 'proposal', 'negotiation', 'closed_won', 'closed_lost'),
    defaultValue: 'qualification'
  },
  probability: {
    type: DataTypes.INTEGER,
    defaultValue: 10,
    validate: {
      min: 0,
      max: 100
    }
  },
  expectedCloseDate: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  actualCloseDate: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  lostReason: {
    type: DataTypes.STRING,
    allowNull: true
  },
  competitorName: {
    type: DataTypes.STRING,
    allowNull: true
  },
  source: {
    type: DataTypes.STRING,
    allowNull: true
  },
  nextStep: {
    type: DataTypes.STRING,
    allowNull: true
  },
  type: {
    type: DataTypes.ENUM('new_business', 'existing_business', 'renewal'),
    defaultValue: 'new_business'
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
    defaultValue: 'medium'
  },
  customerId: {
    type: DataTypes.UUID,
    allowNull: false
  },
  contactId: {
    type: DataTypes.UUID,
    allowNull: true
  },
  assignedToId: {
    type: DataTypes.UUID,
    allowNull: true
  },
  createdById: {
    type: DataTypes.UUID,
    allowNull: true
  }
}, {
  timestamps: true
});

module.exports = Deal;

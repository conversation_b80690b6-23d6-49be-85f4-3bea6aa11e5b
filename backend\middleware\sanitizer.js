/**
 * Input Sanitization Middleware
 * 
 * Sanitizes user input to prevent XSS attacks and other injection vulnerabilities
 */

const validator = require('validator');
const xss = require('xss');

/**
 * Custom XSS filter options
 */
const xssOptions = {
  whiteList: {
    a: ['href', 'title', 'target'],
    b: [],
    i: [],
    u: [],
    strong: [],
    em: [],
    p: [],
    br: [],
    ul: [],
    ol: [],
    li: [],
    blockquote: [],
    code: [],
    pre: []
  },
  stripIgnoreTag: true,
  stripIgnoreTagBody: ['script', 'style']
};

/**
 * Sanitize a single value
 * @param {*} value - Value to sanitize
 * @param {Object} options - Sanitization options
 * @returns {*} Sanitized value
 */
const sanitizeValue = (value, options = {}) => {
  if (value === null || value === undefined) {
    return value;
  }

  // Convert to string for sanitization
  let sanitized = String(value);

  // Apply XSS filter
  if (!options.skipXss) {
    sanitized = xss(sanitized, options.xssOptions || xssOptions);
  }

  // Trim whitespace
  if (!options.skipTrim) {
    sanitized = sanitized.trim();
  }

  // Escape HTML entities
  if (options.escapeHtml) {
    sanitized = validator.escape(sanitized);
  }

  // Remove null bytes
  sanitized = sanitized.replace(/\0/g, '');

  // Additional sanitizations based on field type
  if (options.type) {
    switch (options.type) {
      case 'email':
        sanitized = validator.normalizeEmail(sanitized) || sanitized;
        break;
      case 'url':
        sanitized = validator.isURL(sanitized) ? sanitized : '';
        break;
      case 'alphanumeric':
        sanitized = sanitized.replace(/[^a-zA-Z0-9]/g, '');
        break;
      case 'numeric':
        sanitized = sanitized.replace(/[^0-9]/g, '');
        break;
      case 'phone':
        sanitized = sanitized.replace(/[^0-9+\-() ]/g, '');
        break;
    }
  }

  // Length limit
  if (options.maxLength) {
    sanitized = sanitized.substring(0, options.maxLength);
  }

  return sanitized;
};

/**
 * Recursively sanitize an object
 * @param {Object} obj - Object to sanitize
 * @param {Object} schema - Sanitization schema
 * @returns {Object} Sanitized object
 */
const sanitizeObject = (obj, schema = {}) => {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }

  const sanitized = Array.isArray(obj) ? [] : {};

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key];
      const fieldSchema = schema[key] || {};

      if (Array.isArray(value)) {
        sanitized[key] = value.map(item => 
          typeof item === 'object' ? sanitizeObject(item, fieldSchema) : sanitizeValue(item, fieldSchema)
        );
      } else if (typeof value === 'object' && value !== null) {
        sanitized[key] = sanitizeObject(value, fieldSchema);
      } else {
        sanitized[key] = sanitizeValue(value, fieldSchema);
      }
    }
  }

  return sanitized;
};

/**
 * Middleware to sanitize request body
 */
const sanitizeBody = (schema = {}) => {
  return (req, res, next) => {
    if (req.body) {
      req.body = sanitizeObject(req.body, schema);
    }
    next();
  };
};

/**
 * Middleware to sanitize query parameters
 */
const sanitizeQuery = (schema = {}) => {
  return (req, res, next) => {
    if (req.query) {
      req.query = sanitizeObject(req.query, schema);
    }
    next();
  };
};

/**
 * Middleware to sanitize route parameters
 */
const sanitizeParams = (schema = {}) => {
  return (req, res, next) => {
    if (req.params) {
      req.params = sanitizeObject(req.params, schema);
    }
    next();
  };
};

/**
 * Global sanitization middleware
 * Applies basic sanitization to all requests
 */
const globalSanitizer = (req, res, next) => {
  // Sanitize common fields
  const commonSchema = {
    email: { type: 'email', maxLength: 255 },
    name: { maxLength: 100 },
    title: { maxLength: 200 },
    description: { maxLength: 1000 },
    comment: { maxLength: 500 },
    phone: { type: 'phone', maxLength: 20 },
    url: { type: 'url', maxLength: 500 }
  };

  // Apply to body
  if (req.body) {
    req.body = sanitizeObject(req.body, commonSchema);
  }

  // Apply to query
  if (req.query) {
    // Query params should be more strictly sanitized
    const querySchema = {
      ...commonSchema,
      search: { maxLength: 100 },
      sort: { type: 'alphanumeric', maxLength: 50 },
      page: { type: 'numeric', maxLength: 10 },
      limit: { type: 'numeric', maxLength: 10 }
    };
    req.query = sanitizeObject(req.query, querySchema);
  }

  next();
};

/**
 * Sanitization schemas for specific endpoints
 */
const schemas = {
  user: {
    email: { type: 'email', maxLength: 255 },
    password: { skipXss: true, maxLength: 128 }, // Don't XSS filter passwords
    fullName: { maxLength: 100 },
    username: { type: 'alphanumeric', maxLength: 50 },
    phone: { type: 'phone', maxLength: 20 },
    department: { maxLength: 50 },
    role: { type: 'alphanumeric', maxLength: 20 }
  },
  customer: {
    name: { maxLength: 100 },
    email: { type: 'email', maxLength: 255 },
    phone: { type: 'phone', maxLength: 20 },
    company: { maxLength: 100 },
    address: { maxLength: 500 },
    notes: { maxLength: 1000 }
  },
  deal: {
    title: { maxLength: 200 },
    value: { type: 'numeric', maxLength: 20 },
    stage: { maxLength: 50 },
    probability: { type: 'numeric', maxLength: 3 },
    expectedCloseDate: { maxLength: 10 },
    description: { maxLength: 1000 }
  },
  task: {
    title: { maxLength: 200 },
    description: { maxLength: 1000 },
    priority: { type: 'alphanumeric', maxLength: 20 },
    status: { type: 'alphanumeric', maxLength: 20 },
    dueDate: { maxLength: 10 }
  }
};

/**
 * Validate and sanitize file uploads
 */
const sanitizeFile = (options = {}) => {
  const defaults = {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
    allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.pdf']
  };

  const config = { ...defaults, ...options };

  return (req, res, next) => {
    if (!req.files || req.files.length === 0) {
      return next();
    }

    const files = Array.isArray(req.files) ? req.files : [req.files];

    for (const file of files) {
      // Check file size
      if (file.size > config.maxSize) {
        return res.status(400).json({
          success: false,
          message: `File ${file.originalname} exceeds maximum size of ${config.maxSize / 1024 / 1024}MB`
        });
      }

      // Check MIME type
      if (!config.allowedTypes.includes(file.mimetype)) {
        return res.status(400).json({
          success: false,
          message: `File type ${file.mimetype} is not allowed`
        });
      }

      // Check file extension
      const ext = file.originalname.substring(file.originalname.lastIndexOf('.')).toLowerCase();
      if (!config.allowedExtensions.includes(ext)) {
        return res.status(400).json({
          success: false,
          message: `File extension ${ext} is not allowed`
        });
      }

      // Sanitize filename
      file.originalname = sanitizeValue(file.originalname, {
        maxLength: 255,
        skipXss: true
      }).replace(/[^a-zA-Z0-9.-]/g, '_');
    }

    next();
  };
};

module.exports = {
  sanitizeValue,
  sanitizeObject,
  sanitizeBody,
  sanitizeQuery,
  sanitizeParams,
  globalSanitizer,
  sanitizeFile,
  schemas
};

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Connection</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container mt-5">
        <h1>API Connection Test</h1>
        
        <div class="card mt-4">
            <div class="card-header">
                <h3>Test Results</h3>
            </div>
            <div class="card-body">
                <div id="testResults"></div>
            </div>
        </div>
        
        <div class="mt-4">
            <button class="btn btn-primary" onclick="runTests()">Run Tests</button>
            <button class="btn btn-secondary" onclick="testLogin()">Test Login</button>
            <button class="btn btn-info" onclick="testCustomers()">Test Customers</button>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/api.js"></script>
    <script src="js/common-ui.js"></script>
    
    <script>
        const resultsDiv = document.getElementById('testResults');
        
        function addResult(test, success, message, details = null) {
            const resultHtml = `
                <div class="alert alert-${success ? 'success' : 'danger'} alert-dismissible fade show" role="alert">
                    <strong>${test}:</strong> ${message}
                    ${details ? `<hr><pre class="mb-0">${JSON.stringify(details, null, 2)}</pre>` : ''}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            resultsDiv.insertAdjacentHTML('beforeend', resultHtml);
        }
        
        async function testHealthCheck() {
            try {
                const response = await fetch('http://localhost:5000/api/health');
                const data = await response.json();
                addResult('Health Check', response.ok, response.ok ? 'API is running' : 'API connection failed', data);
                return response.ok;
            } catch (error) {
                addResult('Health Check', false, 'Failed to connect to API', { error: error.message });
                return false;
            }
        }
        
        async function testLogin() {
            try {
                const response = await api.login('<EMAIL>', 'admin123');
                addResult('Login Test', response.success, response.success ? 'Login successful' : 'Login failed', response);
                return response.success;
            } catch (error) {
                addResult('Login Test', false, 'Login request failed', { error: error.message });
                return false;
            }
        }
        
        async function testCustomers() {
            try {
                const response = await api.getCustomers();
                const success = response && (response.data || response.customers || Array.isArray(response));
                addResult('Get Customers', success, success ? 'Successfully fetched customers' : 'Failed to fetch customers', response);
                return success;
            } catch (error) {
                addResult('Get Customers', false, 'Customer request failed', { error: error.message });
                return false;
            }
        }
        
        async function testCreateCustomer() {
            try {
                const testCustomer = {
                    name: 'Test Customer ' + Date.now(),
                    email: 'test' + Date.now() + '@example.com',
                    phone: '555-0000',
                    company: 'Test Company'
                };
                const response = await api.createCustomer(testCustomer);
                const success = response && (response.success || response.id);
                addResult('Create Customer', success, success ? 'Successfully created customer' : 'Failed to create customer', response);
                return success;
            } catch (error) {
                addResult('Create Customer', false, 'Create customer request failed', { error: error.message });
                return false;
            }
        }
        
        async function runTests() {
            resultsDiv.innerHTML = '';
            
            // Test 1: Health Check
            const healthOk = await testHealthCheck();
            
            if (healthOk) {
                // Test 2: Login
                const loginOk = await testLogin();
                
                if (loginOk) {
                    // Test 3: Get Customers
                    await testCustomers();
                    
                    // Test 4: Create Customer
                    await testCreateCustomer();
                }
            }
            
            // Add loading state test
            addResult('Loading States', true, 'Check console for loading state changes');
            
            // Test loading states
            api.onLoadingChange('test', (isLoading) => {
                console.log('Loading state changed:', isLoading);
            });
            
            // Show current authentication status
            addResult('Auth Status', isAuthenticated(), isAuthenticated() ? 'User is authenticated' : 'User is not authenticated', {
                user: getCurrentUser(),
                token: localStorage.getItem('authToken') ? 'Present' : 'Missing'
            });
        }
        
        // Auto-run tests on page load
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runTests, 1000);
        });
    </script>
</body>
</html>

const express = require('express');
const {
  getDeals,
  getDeal,
  createDeal,
  updateDeal,
  deleteDeal,
  getDealStats
} = require('../controllers/dealController');

const { protect, authorize } = require('../middleware/auth');
const asyncHandler = require('../middleware/asyncHandler');
const {
  validateUUID,
  validateRequired,
  validateOptional,
  validateEnum,
  validateDate,
  validatePagination,
  handleValidationErrors,
  body,
  query
} = require('../middleware/validation');

const router = express.Router();

// Validation rules
const validateCreateDeal = [
  validateRequired('title', 'Deal title is required'),
  validateOptional('description'),
  body('value')
    .notEmpty().withMessage('Deal value is required')
    .isNumeric().withMessage('Deal value must be a number')
    .custom(value => value > 0).withMessage('Deal value must be positive'),
  validateEnum('stage', ['prospecting', 'qualification', 'proposal', 'negotiation', 'closed-won', 'closed-lost'], 'Invalid stage'),
  validateEnum('status', ['active', 'inactive'], 'Invalid status'),
  body('probability')
    .optional()
    .isInt({ min: 0, max: 100 }).withMessage('Probability must be between 0 and 100'),
  validateDate('expectedCloseDate', 'Expected close date is required'),
  body('customerId')
    .notEmpty().withMessage('Customer ID is required')
    .isUUID().withMessage('Customer ID must be a valid UUID'),
  body('contactId')
    .optional()
    .isUUID().withMessage('Contact ID must be a valid UUID'),
  body('assignedToId')
    .optional()
    .isUUID().withMessage('Assigned to must be a valid user ID')
];

const validateUpdateDeal = [
  validateOptional('title'),
  validateOptional('description'),
  body('value')
    .optional()
    .isNumeric().withMessage('Deal value must be a number')
    .custom(value => value > 0).withMessage('Deal value must be positive'),
  validateEnum('stage', ['prospecting', 'qualification', 'proposal', 'negotiation', 'closed-won', 'closed-lost'], 'Invalid stage'),
  validateEnum('status', ['active', 'inactive'], 'Invalid status'),
  body('probability')
    .optional()
    .isInt({ min: 0, max: 100 }).withMessage('Probability must be between 0 and 100'),
  body('expectedCloseDate')
    .optional()
    .isISO8601().withMessage('Expected close date must be a valid date'),
  body('customerId')
    .optional()
    .isUUID().withMessage('Customer ID must be a valid UUID'),
  body('contactId')
    .optional()
    .isUUID().withMessage('Contact ID must be a valid UUID'),
  body('assignedToId')
    .optional()
    .isUUID().withMessage('Assigned to must be a valid user ID')
];

const validateGetDeals = [
  ...validatePagination(),
  query('stage')
    .optional()
    .isIn(['prospecting', 'qualification', 'proposal', 'negotiation', 'closed-won', 'closed-lost']).withMessage('Invalid stage'),
  query('status')
    .optional()
    .isIn(['active', 'inactive']).withMessage('Invalid status'),
  query('customerId')
    .optional()
    .isUUID().withMessage('Customer ID must be a valid UUID'),
  query('assignedToId')
    .optional()
    .isUUID().withMessage('Assigned to ID must be a valid UUID'),
  query('minValue')
    .optional()
    .isNumeric().withMessage('Min value must be a number'),
  query('maxValue')
    .optional()
    .isNumeric().withMessage('Max value must be a number')
];

// All routes require authentication
router.use(protect);

// Statistics route
router.get('/stats', asyncHandler(getDealStats));

// CRUD routes
router
  .route('/')
  .get(validateGetDeals, handleValidationErrors, asyncHandler(getDeals))
  .post(validateCreateDeal, handleValidationErrors, asyncHandler(createDeal));

router
  .route('/:id')
  .get(validateUUID('id'), handleValidationErrors, asyncHandler(getDeal))
  .put(validateUUID('id'), validateUpdateDeal, handleValidationErrors, asyncHandler(updateDeal))
  .delete(validateUUID('id'), handleValidationErrors, authorize('admin', 'manager'), asyncHandler(deleteDeal));

module.exports = router;
